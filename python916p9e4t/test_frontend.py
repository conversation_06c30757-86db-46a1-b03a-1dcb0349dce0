#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
前端功能测试脚本
用于验证前端页面是否正常工作
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8080"
API_BASE = f"{BASE_URL}/api"

def test_frontend_pages():
    """测试前端页面是否可以正常访问"""
    print("=== 测试前端页面访问 ===")
    
    pages = [
        ("/", "用户前端首页"),
        ("/user", "用户前端页面"),
        ("/admin", "管理后台页面")
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{url}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 访问成功")
            else:
                print(f"❌ {name}: 访问失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {name}: 访问异常 - {str(e)}")

def test_api_endpoints():
    """测试API端点是否正常工作"""
    print("\n=== 测试API端点 ===")
    
    # 测试数据查询API
    endpoints = [
        ("/sheshi/page?page=1&limit=5", "设施信息查询"),
        ("/anquanguifan/page?page=1&limit=5", "安全规范查询"),
        ("/shigujilu/page?page=1&limit=5", "事故记录查询"),
        ("/discusssheshi/page?page=1&limit=5", "讨论话题查询"),
        ("/yonghu/page?page=1&limit=1", "用户信息查询")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{API_BASE}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    print(f"✅ {name}: API正常")
                else:
                    print(f"⚠️ {name}: API返回错误 - {data.get('msg', '未知错误')}")
            else:
                print(f"❌ {name}: HTTP错误 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {name}: 请求异常 - {str(e)}")

def test_user_registration():
    """测试用户注册功能"""
    print("\n=== 测试用户注册 ===")
    
    test_user = {
        "zhanghao": f"test_user_{int(time.time())}",
        "mima": "123456",
        "xingming": "测试用户",
        "xingbie": "男",
        "shouji": "13800138000",
        "youxiang": "<EMAIL>"
    }
    
    try:
        response = requests.post(f"{API_BASE}/yonghu/register", 
                               json=test_user, 
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print(f"✅ 用户注册: 成功 (账号: {test_user['zhanghao']})")
                return test_user
            else:
                print(f"⚠️ 用户注册: 失败 - {data.get('msg', '未知错误')}")
        else:
            print(f"❌ 用户注册: HTTP错误 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 用户注册: 请求异常 - {str(e)}")
    
    return None

def test_user_login(user_data):
    """测试用户登录功能"""
    if not user_data:
        print("⚠️ 跳过登录测试 (注册失败)")
        return None
        
    print("\n=== 测试用户登录 ===")
    
    login_data = {
        "zhanghao": user_data["zhanghao"],
        "mima": user_data["mima"]
    }
    
    try:
        response = requests.post(f"{API_BASE}/yonghu/login", 
                               json=login_data, 
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print(f"✅ 用户登录: 成功")
                return data.get('token')
            else:
                print(f"⚠️ 用户登录: 失败 - {data.get('msg', '未知错误')}")
        else:
            print(f"❌ 用户登录: HTTP错误 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 用户登录: 请求异常 - {str(e)}")
    
    return None

def test_authenticated_features(token):
    """测试需要认证的功能"""
    if not token:
        print("⚠️ 跳过认证功能测试 (登录失败)")
        return
        
    print("\n=== 测试认证功能 ===")
    
    headers = {"token": token}
    
    # 测试提交反馈
    feedback_data = {
        "fankuileixing": "测试反馈",
        "fankuibiaoti": "前端测试反馈",
        "fankuineirong": "这是一个前端功能测试反馈",
        "zhuangtai": "待处理"
    }
    
    try:
        response = requests.post(f"{API_BASE}/fankuiyijian/save", 
                               json=feedback_data, 
                               headers=headers,
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print("✅ 反馈提交: 成功")
            else:
                print(f"⚠️ 反馈提交: 失败 - {data.get('msg', '未知错误')}")
        else:
            print(f"❌ 反馈提交: HTTP错误 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 反馈提交: 请求异常 - {str(e)}")

def test_static_files():
    """测试静态文件访问"""
    print("\n=== 测试静态文件 ===")
    
    static_files = [
        ("/css/app.88c02d37.css", "CSS文件"),
        ("/js/app.d44003a6.js", "JavaScript文件"),
        ("/img/avator.c58e4651.png", "图片文件")
    ]
    
    for file_path, name in static_files:
        try:
            response = requests.get(f"{BASE_URL}{file_path}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 访问成功")
            else:
                print(f"❌ {name}: 访问失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {name}: 访问异常 - {str(e)}")

def main():
    """主测试函数"""
    print("儿童游乐设施安全评估系统 - 前端功能测试")
    print("=" * 50)
    
    # 测试前端页面
    test_frontend_pages()
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试用户注册
    user_data = test_user_registration()
    
    # 测试用户登录
    token = test_user_login(user_data)
    
    # 测试认证功能
    test_authenticated_features(token)
    
    # 测试静态文件
    test_static_files()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n使用说明：")
    print(f"1. 访问用户前端: {BASE_URL}/")
    print(f"2. 访问管理后台: {BASE_URL}/admin")
    print("3. 使用测试账号登录体验功能")

if __name__ == "__main__":
    main()
