#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动脚本 - 包含前端功能
用于启动儿童游乐设施安全评估系统（包含新的用户前端）
"""

import os
import sys
import time
import webbrowser
from threading import Timer

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def open_browser():
    """延迟打开浏览器"""
    print("正在打开浏览器...")
    webbrowser.open('http://localhost:8080/')

def main():
    """主函数"""
    print("=" * 60)
    print("儿童游乐设施安全评估系统")
    print("Children's Playground Equipment Safety Assessment System")
    print("=" * 60)
    print()
    
    print("🚀 正在启动系统...")
    print()
    
    try:
        # 导入应用
        from main import app
        
        print("✅ 应用加载成功")
        print()
        print("📋 系统功能:")
        print("   • 用户前端界面 - 现代化响应式设计")
        print("   • 设施信息查询 - 详细的设施安全信息")
        print("   • 安全规范浏览 - 最新安全标准和规范")
        print("   • 事故记录查询 - 历史事故数据分析")
        print("   • 反馈意见提交 - 用户问题反馈系统")
        print("   • 安全讨论参与 - 社区安全话题讨论")
        print("   • 重要信息收藏 - 个人信息管理")
        print("   • 管理后台系统 - 完整的后台管理")
        print()
        print("🌐 访问地址:")
        print("   用户前端: http://localhost:8080/")
        print("   用户页面: http://localhost:8080/user")
        print("   管理后台: http://localhost:8080/admin")
        print("   API文档: http://localhost:8080/api/")
        print()
        print("👤 测试账号:")
        print("   管理员: admin / 123456")
        print("   用户: 可通过前端注册新用户")
        print()
        print("🔧 开发工具:")
        print("   前端测试: python test_frontend.py")
        print("   API测试: 使用Postman或curl")
        print()
        
        # 延迟3秒后打开浏览器
        Timer(3.0, open_browser).start()
        
        print("🎯 系统启动中...")
        print("   按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=8080,
            debug=True,
            use_reloader=False  # 避免重复启动
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
