# 儿童游乐设施安全评估系统 - 用户前端

## 概述

这是一个基于HTML、CSS和JavaScript开发的用户前端界面，为儿童游乐设施安全评估系统提供完整的用户交互功能。

## 功能特性

### 🏠 首页
- 系统概览和统计数据
- 功能模块导航
- 响应式设计

### 🎪 信息查询模块
1. **设施信息查询**
   - 查看游乐设施详细信息
   - 按类型、安全等级筛选
   - 设施图片展示
   - 安全等级标识

2. **安全规范浏览**
   - 查看安全规范文档
   - 按分类筛选规范
   - 规范详情查看

3. **事故记录查询**
   - 历史事故记录查看
   - 按严重程度筛选
   - 时间范围筛选

### 🤝 互动功能模块
1. **反馈意见提交**
   - 提交安全问题反馈
   - 设施故障报告
   - 改进建议提交
   - 查看反馈处理状态

2. **安全讨论参与**
   - 发起安全话题讨论
   - 参与讨论交流
   - 按设施分类讨论

3. **重要信息收藏**
   - 收藏设施信息
   - 收藏安全规范
   - 收藏讨论话题
   - 收藏管理

### 👤 用户功能
- 用户注册和登录
- 个人信息管理
- 会话保持

## 技术特点

### 🎨 界面设计
- 现代化响应式设计
- 移动端友好
- 直观的用户界面
- 美观的卡片布局

### ⚡ 交互体验
- 实时数据加载
- 智能搜索筛选
- 模态框详情展示
- 消息提示反馈

### 🔒 安全特性
- 用户身份验证
- Token会话管理
- 数据验证
- 错误处理

## 文件结构

```
python916p9e4t/api/templates/front/front/dist/
├── user-frontend.html          # 主前端页面
├── css/                        # 样式文件
├── js/                         # JavaScript文件
├── img/                        # 图片资源
└── fonts/                      # 字体文件

python916p9e4t/api/
├── frontend_routes.py          # 前端路由
└── __init__.py                 # 应用初始化（已修改）
```

## 使用方法

### 1. 启动应用
```bash
cd python916p9e4t
python main.py
```

### 2. 访问前端
- 用户前端：http://localhost:8080/
- 用户前端：http://localhost:8080/user
- 管理后台：http://localhost:8080/admin

### 3. 功能使用

#### 游客模式
- 浏览设施信息
- 查看安全规范
- 查看事故记录
- 浏览讨论话题

#### 登录用户
- 所有游客功能
- 提交反馈意见
- 发起安全讨论
- 收藏重要信息
- 查看个人反馈记录

## API集成

前端通过以下API端点与后端交互：

### 用户认证
- `POST /api/yonghu/login` - 用户登录
- `POST /api/yonghu/register` - 用户注册
- `GET /api/yonghu/session` - 会话验证

### 数据查询
- `GET /api/sheshi/page` - 设施信息分页查询
- `GET /api/anquanguifan/page` - 安全规范分页查询
- `GET /api/shigujilu/page` - 事故记录分页查询
- `GET /api/discusssheshi/page` - 讨论话题分页查询

### 详情查看
- `GET /api/sheshi/info/{id}` - 设施详情
- `GET /api/anquanguifan/info/{id}` - 规范详情
- `GET /api/shigujilu/info/{id}` - 事故详情
- `GET /api/discusssheshi/info/{id}` - 讨论详情

### 用户操作
- `POST /api/fankuiyijian/save` - 提交反馈
- `POST /api/discusssheshi/save` - 发起讨论
- `POST /api/storeup/save` - 添加收藏
- `POST /api/storeup/delete` - 取消收藏

## 响应式设计

### 桌面端 (>768px)
- 完整导航菜单
- 多列卡片布局
- 大尺寸图片展示

### 移动端 (≤768px)
- 简化导航
- 单列布局
- 优化触摸交互

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 开发说明

### 自定义样式
所有样式都内嵌在HTML文件中，可以直接修改`<style>`标签内的CSS。

### 添加新功能
1. 在HTML中添加新的页面结构
2. 在JavaScript中添加对应的事件处理
3. 实现API调用和数据渲染

### 调试
使用浏览器开发者工具查看控制台输出和网络请求。

## 注意事项

1. 确保后端API服务正常运行
2. 检查数据库连接配置
3. 确认静态文件路径正确
4. 注意跨域请求设置

## 更新日志

### v1.0.0 (2024-02-27)
- 初始版本发布
- 实现所有核心功能
- 完整的用户界面
- 响应式设计支持

## 支持

如有问题或建议，请联系开发团队。
