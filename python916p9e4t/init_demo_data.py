#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示数据初始化脚本
为前端功能测试创建一些示例数据
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# API配置
API_BASE = "http://localhost:8080/api"

def create_demo_facilities():
    """创建演示设施数据"""
    print("创建演示设施数据...")
    
    facilities = [
        {
            "sheshimingcheng": "彩虹滑梯",
            "sheshileixing": "滑梯",
            "weizhi": "A区游乐场",
            "zhuangtai": "正常运行",
            "anquandengji": "安全",
            "shiyongpinlv": "高",
            "weihuzhuangtai": "良好",
            "beizhu": "适合3-12岁儿童使用"
        },
        {
            "sheshimingcheng": "旋转木马",
            "sheshileixing": "转椅",
            "weizhi": "B区游乐场",
            "zhuangtai": "正常运行",
            "anquandengji": "安全",
            "shiyongpinlv": "中",
            "weihuzhuangtai": "良好",
            "beizhu": "经典旋转木马，深受儿童喜爱"
        },
        {
            "sheshimingcheng": "攀爬网",
            "sheshileixing": "攀爬架",
            "weizhi": "C区游乐场",
            "zhuangtai": "维护中",
            "anquandengji": "警告",
            "shiyongpinlv": "低",
            "weihuzhuangtai": "需要维护",
            "beizhu": "部分绳网需要更换"
        },
        {
            "sheshimingcheng": "双人秋千",
            "sheshileixing": "秋千",
            "weizhi": "D区游乐场",
            "zhuangtai": "正常运行",
            "anquandengji": "安全",
            "shiyongpinlv": "高",
            "weihuzhuangtai": "良好",
            "beizhu": "新安装的安全秋千"
        },
        {
            "sheshimingcheng": "小象跷跷板",
            "sheshileixing": "跷跷板",
            "weizhi": "E区游乐场",
            "zhuangtai": "正常运行",
            "anquandengji": "安全",
            "shiyongpinlv": "中",
            "weihuzhuangtai": "良好",
            "beizhu": "可爱的小象造型跷跷板"
        }
    ]
    
    for facility in facilities:
        try:
            response = requests.post(f"{API_BASE}/sheshi/save", json=facility)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    print(f"✅ 创建设施: {facility['sheshimingcheng']}")
                else:
                    print(f"⚠️ 设施创建失败: {facility['sheshimingcheng']} - {data.get('msg')}")
        except Exception as e:
            print(f"❌ 设施创建异常: {facility['sheshimingcheng']} - {str(e)}")

def create_demo_safety_rules():
    """创建演示安全规范数据"""
    print("\n创建演示安全规范数据...")
    
    safety_rules = [
        {
            "guifanbiaoti": "儿童游乐设施安全技术规范",
            "guifanleixing": "国家标准",
            "fabujigou": "国家质量监督检验检疫总局",
            "shengxiaoriqi": "2024-01-01",
            "guifanneirong": "本规范规定了儿童游乐设施的安全技术要求，包括设计、制造、安装、使用、维护等各个环节的安全标准。适用于供14岁以下儿童使用的游乐设施。"
        },
        {
            "guifanbiaoti": "游乐设施日常检查规程",
            "guifanleixing": "操作指南",
            "fabujigou": "中国游艺机游乐园协会",
            "shengxiaoriqi": "2024-01-15",
            "guifanneirong": "规定了游乐设施日常检查的内容、方法、频次和记录要求。包括开园前检查、运营中巡检、闭园后检查等。"
        },
        {
            "guifanbiaoti": "儿童游乐场地面安全标准",
            "guifanleixing": "行业规范",
            "fabujigou": "住房和城乡建设部",
            "shengxiaoriqi": "2024-02-01",
            "guifanneirong": "规定了儿童游乐场地面材料的安全要求，包括防滑、缓冲、环保等性能指标，以及铺装工艺和验收标准。"
        },
        {
            "guifanbiaoti": "游乐设施维护保养规范",
            "guifanleixing": "维护标准",
            "fabujigou": "中国特种设备检测研究院",
            "shengxiaoriqi": "2024-02-15",
            "guifanneirong": "详细规定了各类游乐设施的维护保养周期、内容、方法和质量要求，确保设施长期安全运行。"
        }
    ]
    
    for rule in safety_rules:
        try:
            response = requests.post(f"{API_BASE}/anquanguifan/save", json=rule)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    print(f"✅ 创建规范: {rule['guifanbiaoti']}")
                else:
                    print(f"⚠️ 规范创建失败: {rule['guifanbiaoti']} - {data.get('msg')}")
        except Exception as e:
            print(f"❌ 规范创建异常: {rule['guifanbiaoti']} - {str(e)}")

def create_demo_accidents():
    """创建演示事故记录数据"""
    print("\n创建演示事故记录数据...")
    
    accidents = [
        {
            "shigushijian": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
            "yanzhongchengdu": "轻微",
            "sheshimingcheng": "彩虹滑梯",
            "shigudidian": "A区游乐场",
            "shigumiaoshu": "儿童在滑梯上玩耍时轻微擦伤，已及时处理。无需特殊治疗。",
            "chulicuoshi": "清洁伤口，贴创可贴。加强现场安全提醒。"
        },
        {
            "shigushijian": (datetime.now() - timedelta(days=15)).strftime("%Y-%m-%d %H:%M:%S"),
            "yanzhongchengdu": "一般",
            "sheshimingcheng": "攀爬网",
            "shigudidian": "C区游乐场",
            "shigumiaoshu": "攀爬网部分绳索松动，儿童攀爬时发现异常。未造成人员伤害。",
            "chulicuoshi": "立即停止使用，更换松动绳索，全面检查设施安全性。"
        },
        {
            "shigushijian": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"),
            "yanzhongchengdu": "轻微",
            "sheshimingcheng": "双人秋千",
            "shigudidian": "D区游乐场",
            "shigumiaoshu": "两名儿童同时使用秋千时发生轻微碰撞，无明显外伤。",
            "chulicuoshi": "安抚儿童情绪，检查身体状况。加强使用规则宣传。"
        }
    ]
    
    for accident in accidents:
        try:
            response = requests.post(f"{API_BASE}/shigujilu/save", json=accident)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    print(f"✅ 创建事故记录: {accident['sheshimingcheng']} - {accident['yanzhongchengdu']}")
                else:
                    print(f"⚠️ 事故记录创建失败: {accident['sheshimingcheng']} - {data.get('msg')}")
        except Exception as e:
            print(f"❌ 事故记录创建异常: {accident['sheshimingcheng']} - {str(e)}")

def create_demo_discussions():
    """创建演示讨论数据"""
    print("\n创建演示讨论数据...")
    
    discussions = [
        {
            "taolunzhuti": "如何提高儿童游乐设施的安全性？",
            "taolunneirong": "大家觉得在日常使用中，我们应该如何更好地保障儿童游乐设施的安全？欢迎分享经验和建议。",
            "faburen": "安全管理员",
            "faburenid": 1
        },
        {
            "taolunzhuti": "雨天游乐设施使用注意事项",
            "taolunneirong": "雨天或潮湿环境下，游乐设施的使用有哪些特殊注意事项？如何防止滑倒等意外？",
            "faburen": "设施维护员",
            "faburenid": 2
        },
        {
            "taolunzhuti": "新型游乐设施的安全评估标准",
            "taolunneirong": "随着技术发展，新型游乐设施不断涌现。我们应该如何建立相应的安全评估标准？",
            "faburen": "技术专家",
            "faburenid": 3
        }
    ]
    
    for discussion in discussions:
        try:
            response = requests.post(f"{API_BASE}/discusssheshi/save", json=discussion)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    print(f"✅ 创建讨论: {discussion['taolunzhuti']}")
                else:
                    print(f"⚠️ 讨论创建失败: {discussion['taolunzhuti']} - {data.get('msg')}")
        except Exception as e:
            print(f"❌ 讨论创建异常: {discussion['taolunzhuti']} - {str(e)}")

def main():
    """主函数"""
    print("儿童游乐设施安全评估系统 - 演示数据初始化")
    print("=" * 50)
    
    print("⚠️  注意：请确保系统已启动并运行在 http://localhost:8080")
    print()
    
    try:
        # 测试API连接
        response = requests.get(f"{API_BASE}/yonghu/page?page=1&limit=1", timeout=5)
        if response.status_code != 200:
            print("❌ 无法连接到API服务，请先启动系统")
            return
        
        print("✅ API连接正常，开始创建演示数据...")
        print()
        
        # 创建各类演示数据
        create_demo_facilities()
        create_demo_safety_rules()
        create_demo_accidents()
        create_demo_discussions()
        
        print()
        print("=" * 50)
        print("✅ 演示数据创建完成！")
        print()
        print("现在可以访问前端页面查看数据：")
        print("🌐 http://localhost:8080/")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到系统，请确保系统已启动")
        print("💡 请先运行: python start_with_frontend.py")
    except Exception as e:
        print(f"❌ 创建演示数据时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
