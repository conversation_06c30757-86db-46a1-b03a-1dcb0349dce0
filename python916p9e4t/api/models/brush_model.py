# coding:utf-8
import random
from datetime import datetime
from sqlalchemy import text,TIMESTAMP

from api.models.models import Base_model
from api.exts import db
from sqlalchemy.dialects.mysql import DOUBLE,LONGTEXT
# 个人信息
class yunyingfang(Base_model):
    __doc__ = u'''yunyingfang'''
    __tablename__ = 'yunyingfang'

    __loginUser__='yunyingfangzhanghao'


    __authTables__={}
    __authPeople__='是'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='否'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    yunyingfangzhanghao=db.Column( db.VARCHAR(255), nullable=False,unique=True,comment='运营方账号' )
    mima=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='密码' )
    fuzeren=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='负责人' )
    xingbie=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='性别' )
    nianling=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='年龄' )
    shoujihao=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='手机号' )
    shenfenzhenghao=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='身份证号' )
    touxiang=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )

class jianguanrenyuan(Base_model):
    __doc__ = u'''jianguanrenyuan'''
    __tablename__ = 'jianguanrenyuan'

    __loginUser__='jianguanzhanghao'


    __authTables__={}
    __authPeople__='是'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='否'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    jianguanzhanghao=db.Column( db.VARCHAR(255), nullable=False,unique=True,comment='监管账号' )
    mima=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='密码' )
    touxiang=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )
    jianguanxingming=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='监管姓名' )
    xingbie=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='性别' )
    jianguandianhua=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='监管电话' )

class yonghu(Base_model):
    __doc__ = u'''yonghu'''
    __tablename__ = 'yonghu'

    __loginUser__='zhanghao'


    __authTables__={}
    __authPeople__='是'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='否'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    zhanghao=db.Column( db.VARCHAR(255), nullable=False,unique=True,comment='账号' )
    mima=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='密码' )
    xingming=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='姓名' )
    xingbie=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='性别' )
    shouji=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='手机' )
    youxiang=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='邮箱' )
    shenfenzheng=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='身份证' )
    touxiang=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )

class facilities(Base_model):
    __doc__ = u'''facilities'''
    __tablename__ = 'facilities'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='是'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='是'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    ssbh=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设施编号' )
    ssmc=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设施名称' )
    synl=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='适用年龄' )
    sszt=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设施状态' )
    sypl=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='使用频率' )
    sysc=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='使用时长' )
    tqzk=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='天气状况' )
    aqyh=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='安全隐患' )
    aqdj=db.Column( db.Integer, default=0 ,  nullable=True, unique=False,comment='安全等级' )
    gjjy=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='改进建议' )
    sstp=db.Column( db.Text,  nullable=True, unique=False,comment='设施照片' )
    thumbsupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='赞' )
    crazilynum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='踩' )
    discussnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='评论数' )
    storeupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='收藏数' )

class facilitiesforecast(Base_model):
    __doc__ = u'''facilitiesforecast'''
    __tablename__ = 'facilitiesforecast'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='否'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    ssmc=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设施名称' )
    synl=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='适用年龄' )
    sypl=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='使用频率' )
    sysc=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='使用时长' )
    tqzk=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='天气状况' )
    aqyh=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='安全隐患' )
    sszt=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设施状态' )

class shebeiweihujilu(Base_model):
    __doc__ = u'''shebeiweihujilu'''
    __tablename__ = 'shebeiweihujilu'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='是'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    shebeimingcheng=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设备名称' )
    shebeijianjie=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设备简介' )
    shebeiyongtu=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='设备用途' )
    shebeitupian=db.Column( db.Text,  nullable=True, unique=False,comment='设备图片' )
    shebeishuliang=db.Column( db.Integer, default=0 ,  nullable=True, unique=False,comment='设备数量' )
    weihujilu=db.Column( db.Text,  nullable=True, unique=False,comment='维护记录' )
    weihushijian=db.Column( db.DateTime,  nullable=True, unique=False,comment='维护时间' )
    zerenrenyuan=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='责任人员' )

class shigujilu(Base_model):
    __doc__ = u'''shigujilu'''
    __tablename__ = 'shigujilu'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='是'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    shebeimingcheng=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='设备名称' )
    shiguleixing=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='事故类型' )
    shigushijian=db.Column( db.DateTime, nullable=False, unique=False,comment='事故时间' )
    shigudidian=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='事故地点' )
    shiguxiangqing=db.Column( db.Text,  nullable=True, unique=False,comment='事故详情' )
    jilushijian=db.Column( db.Date,  nullable=True, unique=False,comment='记录时间' )
    chulizhuangtai=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='处理状态' )
    fengmian=db.Column( db.Text,  nullable=True, unique=False,comment='封面' )

class renyuanpeixun(Base_model):
    __doc__ = u'''renyuanpeixun'''
    __tablename__ = 'renyuanpeixun'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='否'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    peixunbiaoti=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='培训标题' )
    renyuanxingming=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='人员姓名' )
    peixunneirong=db.Column( db.Text, nullable=False, unique=False,comment='培训内容' )
    shoujihaoma=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='手机号码' )
    peixunshijian=db.Column( db.DateTime,  nullable=True, unique=False,comment='培训时间' )

class anquanguifan(Base_model):
    __doc__ = u'''anquanguifan'''
    __tablename__ = 'anquanguifan'



    __authTables__={}
    __authPeople__='否'
    __authSeparate__='否'
    __thumbsUp__='否'
    __intelRecom__='否'
    __browseClick__='否'
    __foreEndListAuth__='否'
    __foreEndList__='是'
    __isAdmin__='否'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    biaoti=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='标题' )
    fengmian=db.Column( db.Text,  nullable=True, unique=False,comment='封面' )
    laiyuan=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='来源' )
    guizhangzhidu=db.Column( db.Text,  nullable=True, unique=False,comment='规章制度' )
    neirong=db.Column( db.Text,  nullable=True, unique=False,comment='内容' )
    faburiqi=db.Column( db.Date,  nullable=True, unique=False,comment='发布日期' )
    storeupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='收藏数' )

class chat(Base_model):
    __doc__ = u'''chat'''
    __tablename__ = 'chat'



    __authTables__={}
    __foreEndListAuth__='是'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    userid=db.Column( db.BigInteger, default=0 , nullable=False, unique=False,comment='用户id' )
    adminid=db.Column( db.BigInteger, default=0 ,  nullable=True, unique=False,comment='管理员id' )
    ask=db.Column( db.Text,  nullable=True, unique=False,comment='提问' )
    reply=db.Column( db.Text,  nullable=True, unique=False,comment='回复' )
    isreply=db.Column( db.Integer, default=0 ,  nullable=True, unique=False,comment='是否回复' )
    isread=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='已读/未读(1:已读,0:未读)' )
    uname=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='用户头像' )
    uimage=db.Column( db.Text,  nullable=True, unique=False,comment='用户名' )
    type=db.Column( db.Integer,default=1 ,  nullable=True, unique=False,comment='内容类型(1:文本,2:图片,3:视频,4:文件,5:表情)' )

class newstype(Base_model):
    __doc__ = u'''newstype'''
    __tablename__ = 'newstype'



    __authTables__={}
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    typename=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='分类名称' )

class news(Base_model):
    __doc__ = u'''news'''
    __tablename__ = 'news'



    __authTables__={}
    __thumbsUp__='是'
    __intelRecom__='是'
    __browseClick__='是'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    title=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='标题' )
    introduction=db.Column( db.Text,  nullable=True, unique=False,comment='简介' )
    typename=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='分类名称' )
    name=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='发布人' )
    headportrait=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )
    clicknum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='点击次数' )
    clicktime=db.Column( db.DateTime,  nullable=True, unique=False,comment='最近点击时间' )
    thumbsupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='赞' )
    crazilynum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='踩' )
    storeupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='收藏数' )
    picture=db.Column( db.Text, nullable=False, unique=False,comment='图片' )
    content=db.Column( db.Text, nullable=False, unique=False,comment='内容' )

class storeup(Base_model):
    __doc__ = u'''storeup'''
    __tablename__ = 'storeup'



    __authTables__={}
    __authSeparate__='是'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    userid=db.Column( db.BigInteger, default=0 , nullable=False, unique=False,comment='用户id' )
    refid=db.Column( db.BigInteger, default=0 ,  nullable=True, unique=False,comment='商品id' )
    tablename=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='表名' )
    name=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='名称' )
    picture=db.Column( db.Text,  nullable=True, unique=False,comment='图片' )
    type=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='类型' )
    inteltype=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='推荐类型' )
    remark=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='备注' )

class aboutus(Base_model):
    __doc__ = u'''aboutus'''
    __tablename__ = 'aboutus'



    __authTables__={}
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    title=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='标题' )
    subtitle=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='副标题' )
    content=db.Column( db.Text, nullable=False, unique=False,comment='内容' )
    picture1=db.Column( db.Text,  nullable=True, unique=False,comment='图片1' )
    picture2=db.Column( db.Text,  nullable=True, unique=False,comment='图片2' )
    picture3=db.Column( db.Text,  nullable=True, unique=False,comment='图片3' )

class systemintro(Base_model):
    __doc__ = u'''systemintro'''
    __tablename__ = 'systemintro'



    __authTables__={}
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    title=db.Column( db.VARCHAR(255), nullable=False, unique=False,comment='标题' )
    subtitle=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='副标题' )
    content=db.Column( db.Text, nullable=False, unique=False,comment='内容' )
    picture1=db.Column( db.Text,  nullable=True, unique=False,comment='图片1' )
    picture2=db.Column( db.Text,  nullable=True, unique=False,comment='图片2' )
    picture3=db.Column( db.Text,  nullable=True, unique=False,comment='图片3' )

class messages(Base_model):
    __doc__ = u'''messages'''
    __tablename__ = 'messages'



    __authTables__={}
    __hasMessage__='是'
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    userid=db.Column( db.BigInteger, default=0 , nullable=False, unique=False,comment='留言人id' )
    username=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='用户名' )
    avatarurl=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )
    content=db.Column( db.Text, nullable=False, unique=False,comment='留言内容' )
    cpicture=db.Column( db.Text,  nullable=True, unique=False,comment='留言图片' )
    reply=db.Column( db.Text,  nullable=True, unique=False,comment='回复内容' )
    rpicture=db.Column( db.Text,  nullable=True, unique=False,comment='回复图片' )

class discussfacilities(Base_model):
    __doc__ = u'''discussfacilities'''
    __tablename__ = 'discussfacilities'



    __authTables__={}
    id = db.Column(db.BigInteger, primary_key=True,autoincrement=False,comment='主键')
    addtime = db.Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))
    refid=db.Column( db.BigInteger, default=0 , nullable=False, unique=False,comment='关联表id' )
    userid=db.Column( db.BigInteger, default=0 , nullable=False, unique=False,comment='用户id' )
    avatarurl=db.Column( db.Text,  nullable=True, unique=False,comment='头像' )
    nickname=db.Column( db.VARCHAR(255),  nullable=True, unique=False,comment='用户名' )
    content=db.Column( db.Text, nullable=False, unique=False,comment='评论内容' )
    reply=db.Column( db.Text,  nullable=True, unique=False,comment='回复内容' )
    thumbsupnum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='赞' )
    crazilynum=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='踩' )
    istop=db.Column( db.Integer,default=0 ,  nullable=True, unique=False,comment='置顶(1:置顶,0:非置顶)' )
    tuserids=db.Column( db.Text,  nullable=True, unique=False,comment='赞用户ids' )
    cuserids=db.Column( db.Text,  nullable=True, unique=False,comment='踩用户ids' )

