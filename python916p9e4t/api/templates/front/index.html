<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大数据在儿童游乐设施安全性评估中的应用</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 60px 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1em;
            line-height: 1.6;
        }
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 150px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .features {
            margin-top: 40px;
            text-align: left;
        }
        .feature {
            margin: 15px 0;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .feature-desc {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ 儿童游乐设施安全评估系统</h1>
        <p class="subtitle">
            基于大数据技术的智能化儿童游乐设施安全性评估与管理平台<br>
            为儿童游乐安全保驾护航
        </p>
        
        <div class="buttons">
            <a href="/front" class="btn btn-primary">🎮 用户前台</a>
            <a href="/admin" class="btn btn-secondary">⚙️ 管理后台</a>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-title">🔍 智能安全评估</div>
                <div class="feature-desc">运用大数据分析技术，对游乐设施进行全方位安全性评估</div>
            </div>
            <div class="feature">
                <div class="feature-title">📊 实时监控预警</div>
                <div class="feature-desc">实时监控设施状态，智能预警潜在安全风险</div>
            </div>
            <div class="feature">
                <div class="feature-title">📋 规范化管理</div>
                <div class="feature-desc">标准化的设施管理流程，确保安全规范执行</div>
            </div>
            <div class="feature">
                <div class="feature-title">🤖 AI预测分析</div>
                <div class="feature-desc">基于机器学习的故障预测和维护建议</div>
            </div>
        </div>
    </div>
</body>
</html>
