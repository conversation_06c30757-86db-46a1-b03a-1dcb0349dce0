import Vue from 'vue';
//配置路由
import VueRouter from 'vue-router'
Vue.use(VueRouter);
//1.创建组件
import Index from '@/views/index'
import Home from '@/views/home'
import Board from '@/views/board'
import Login from '@/views/login'
import NotFound from '@/views/404'
import UpdatePassword from '@/views/update-password'
import pay from '@/views/pay'
import register from '@/views/register'
import center from '@/views/center'
	import news from '@/views/modules/news/list'
	import aboutus from '@/views/modules/aboutus/list'
	import shigujilu from '@/views/modules/shigujilu/list'
	import jianguanrenyuan from '@/views/modules/jianguanrenyuan/list'
	import discussfacilities from '@/views/modules/discussfacilities/list'
	import storeup from '@/views/modules/storeup/list'
	import renyuanpeixun from '@/views/modules/renyuanpeixun/list'
	import yunyingfang from '@/views/modules/yunyingfang/list'
	import facilitiesforecast from '@/views/modules/facilitiesforecast/list'
	import systemintro from '@/views/modules/systemintro/list'
	import yonghu from '@/views/modules/yonghu/list'
	import chat from '@/views/modules/chat/list'
	import messages from '@/views/modules/messages/list'
	import facilities from '@/views/modules/facilities/list'
	import shebeiweihujilu from '@/views/modules/shebeiweihujilu/list'
	import anquanguifan from '@/views/modules/anquanguifan/list'
	import config from '@/views/modules/config/list'
	import newstype from '@/views/modules/newstype/list'


//2.配置路由   注意：名字
export const routes = [{
	path: '/',
	name: '系统首页',
	component: Index,
	children: [{
		// 这里不设置值，是把main作为默认页面
		path: '/',
		name: '系统首页',
		component: Home,
		meta: {icon:'', title:'center', affix: true}
	}, {
		path: '/updatePassword',
		name: '修改密码',
		component: UpdatePassword,
		meta: {icon:'', title:'updatePassword'}
	}, {
		path: '/pay',
		name: '支付',
		component: pay,
		meta: {icon:'', title:'pay'}
	}, {
		path: '/center',
		name: '个人信息',
		component: center,
		meta: {icon:'', title:'center'}
	}
	,{
		path: '/news',
		name: '安全信息',
		component: news
	}
	,{
		path: '/aboutus',
		name: '关于我们',
		component: aboutus
	}
	,{
		path: '/shigujilu',
		name: '事故记录',
		component: shigujilu
	}
	,{
		path: '/jianguanrenyuan',
		name: '监管人员',
		component: jianguanrenyuan
	}
	,{
		path: '/discussfacilities',
		name: '游乐设施',
		component: discussfacilities
	}
	,{
		path: '/storeup',
		name: '我的收藏',
		component: storeup
	}
	,{
		path: '/renyuanpeixun',
		name: '人员培训',
		component: renyuanpeixun
	}
	,{
		path: '/yunyingfang',
		name: '运营方',
		component: yunyingfang
	}
	,{
		path: '/facilitiesforecast',
		name: '安全预测',
		component: facilitiesforecast
	}
	,{
		path: '/systemintro',
		name: '系统简介',
		component: systemintro
	}
	,{
		path: '/yonghu',
		name: '用户',
		component: yonghu
	}
	,{
		path: '/chat',
		name: '客服管理',
		component: chat
	}
	,{
		path: '/messages',
		name: '留言反馈',
		component: messages
	}
	,{
		path: '/facilities',
		name: '游乐设施',
		component: facilities
	}
	,{
		path: '/shebeiweihujilu',
		name: '设备维护记录',
		component: shebeiweihujilu
	}
	,{
		path: '/anquanguifan',
		name: '安全规范',
		component: anquanguifan
	}
	,{
		path: '/config',
		name: '轮播图管理',
		component: config
	}
	,{
		path: '/newstype',
		name: '安全信息分类',
		component: newstype
	}
	]
	},
	{
		path: '/login',
		name: 'login',
		component: Login,
		meta: {icon:'', title:'login'}
	},
	{
		path: '/board',
		name: 'board',
		component: Board,
		meta: {icon:'', title:'board'}
	},
	{
		path: '/register',
		name: 'register',
		component: register,
		meta: {icon:'', title:'register'}
	},
	{
		path: '*',
		component: NotFound
	}
]
//3.实例化VueRouter  注意：名字
const router = new VueRouter({
	mode: 'hash',
	/*hash模式改为history*/
	routes // （缩写）相当于 routes: routes
})
const originalPush = VueRouter.prototype.push
//修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err)
}
export default router;
