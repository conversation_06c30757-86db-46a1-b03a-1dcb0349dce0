<template>
	<div :style='{"width":"calc(100% - 35px)","margin":"10px 20px","fontSize":"15px"}'>
		<el-form
			:style='{"border":"1px solid #BFBFBF","padding":"40px 20% 20px 15%","background":"#fff"}'
			class="add-update-preview"
			ref="ruleForm"
			:model="ruleForm"
			label-width="120px"
		>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yunyingfang'"  label="运营方账号" prop="yunyingfangzhanghao">
					<el-input v-model="ruleForm.yunyingfangzhanghao" readonly						placeholder="运营方账号" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yunyingfang'"  label="负责人" prop="fuzeren">
					<el-input v-model="ruleForm.fuzeren" 						placeholder="负责人" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='yunyingfang'"  label="性别" prop="xingbie">
					<el-select v-model="ruleForm.xingbie"  placeholder="请选择性别">
						<el-option
							v-for="(item,index) in yunyingfangxingbieOptions"
							v-bind:key="index"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yunyingfang'"  label="年龄" prop="nianling">
					<el-input v-model="ruleForm.nianling" 						placeholder="年龄" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yunyingfang'"  label="手机号" prop="shoujihao">
					<el-input v-model="ruleForm.shoujihao" 						placeholder="手机号" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yunyingfang'"  label="身份证号" prop="shenfenzhenghao">
					<el-input v-model="ruleForm.shenfenzhenghao" 						placeholder="身份证号" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='yunyingfang'" label="头像" prop="touxiang">
					<file-upload
						tip="点击上传头像"
						action="file/upload"
						:limit="3"
						:multiple="true"
						:fileUrls="ruleForm.touxiang?ruleForm.touxiang:''"
						@change="yunyingfangtouxiangUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='jianguanrenyuan'"  label="监管账号" prop="jianguanzhanghao">
					<el-input v-model="ruleForm.jianguanzhanghao" readonly						placeholder="监管账号" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='jianguanrenyuan'" label="头像" prop="touxiang">
					<file-upload
						tip="点击上传头像"
						action="file/upload"
						:limit="3"
						:multiple="true"
						:fileUrls="ruleForm.touxiang?ruleForm.touxiang:''"
						@change="jianguanrenyuantouxiangUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='jianguanrenyuan'"  label="监管姓名" prop="jianguanxingming">
					<el-input v-model="ruleForm.jianguanxingming" 						placeholder="监管姓名" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='jianguanrenyuan'"  label="性别" prop="xingbie">
					<el-select v-model="ruleForm.xingbie"  placeholder="请选择性别">
						<el-option
							v-for="(item,index) in jianguanrenyuanxingbieOptions"
							v-bind:key="index"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='jianguanrenyuan'"  label="监管电话" prop="jianguandianhua">
					<el-input v-model="ruleForm.jianguandianhua" 						placeholder="监管电话" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yonghu'"  label="账号" prop="zhanghao">
					<el-input v-model="ruleForm.zhanghao" readonly						placeholder="账号" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yonghu'"  label="姓名" prop="xingming">
					<el-input v-model="ruleForm.xingming" 						placeholder="姓名" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='yonghu'"  label="性别" prop="xingbie">
					<el-select v-model="ruleForm.xingbie"  placeholder="请选择性别">
						<el-option
							v-for="(item,index) in yonghuxingbieOptions"
							v-bind:key="index"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yonghu'"  label="手机" prop="shouji">
					<el-input v-model="ruleForm.shouji" 						placeholder="手机" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yonghu'"  label="邮箱" prop="youxiang">
					<el-input v-model="ruleForm.youxiang" 						placeholder="邮箱" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}'   v-if="flag=='yonghu'"  label="身份证" prop="shenfenzheng">
					<el-input v-model="ruleForm.shenfenzheng" 						placeholder="身份证" clearable></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='yonghu'" label="头像" prop="touxiang">
					<file-upload
						tip="点击上传头像"
						action="file/upload"
						:limit="3"
						:multiple="true"
						:fileUrls="ruleForm.touxiang?ruleForm.touxiang:''"
						@change="yonghutouxiangUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='users'" label="用户名" prop="username">
					<el-input v-model="ruleForm.username" placeholder="用户名"></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","width":"80%","padding":"0","margin":"0 0 20px 0","display":"inline-block"}' v-if="flag=='users'" label="头像" prop="image">
					<file-upload
						tip="点击上传头像"
						action="file/upload"
						:limit="1"
						:multiple="false"
						:fileUrls="ruleForm.image?ruleForm.image:''"
						@change="usersimageUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item :style='{"padding":"0","margin":"20px 0 0"}'>
					<el-button class="btn3" :style='{"border":"0px solid #ccc","cursor":"pointer","padding":"0 10px","margin":"0 10px 0 0","color":"#fff","borderRadius":"4px","background":"#F5C5A9","width":"auto","fontSize":"16px","minWidth":"110px","height":"40px"}' type="primary" @click="onUpdateHandler">
						<span class="icon iconfont icon-xihuan" :style='{"margin":"0 2px","fontSize":"14px","color":"#fff","display":"none","height":"40px"}'></span>
						提交
					</el-button>
				</el-form-item>
		</el-form>
	</div>
</template>
<script>
// 校验引入
import { 
	isMobile,
} from "@/utils/validate";

export default {
	data() {
		return {
			ruleForm: {},
			flag: '',
			usersFlag: false,
			yunyingfangxingbieOptions: [],
			jianguanrenyuanxingbieOptions: [],
			yonghuxingbieOptions: [],
		};
	},
	mounted() {
		var table = this.$storage.get("sessionTable");
		this.flag = table;
		this.$http({
			url: `${this.$storage.get("sessionTable")}/session`,
			method: "get"
		}).then(({ data }) => {
			if (data && data.code === 0) {
				this.ruleForm = data.data;
			} else {
				this.$message.error(data.msg);
			}
		});
		this.yunyingfangxingbieOptions = "男,女".split(',')
		this.jianguanrenyuanxingbieOptions = "男,女".split(',')
		this.yonghuxingbieOptions = "男,女".split(',')
	},
	methods: {
		yunyingfangtouxiangUploadChange(fileUrls) {
			this.ruleForm.touxiang = fileUrls;
		},
		jianguanrenyuantouxiangUploadChange(fileUrls) {
			this.ruleForm.touxiang = fileUrls;
		},
		yonghutouxiangUploadChange(fileUrls) {
			this.ruleForm.touxiang = fileUrls;
		},
		usersimageUploadChange(fileUrls) {
			this.ruleForm.image = fileUrls;
		},
		onUpdateHandler() {
			if((!this.ruleForm.yunyingfangzhanghao)&& 'yunyingfang'==this.flag){
				this.$message.error('运营方账号不能为空');
				return
			}


			if((!this.ruleForm.mima)&& 'yunyingfang'==this.flag){
				this.$message.error('密码不能为空');
				return
			}

			if((!this.ruleForm.fuzeren)&& 'yunyingfang'==this.flag){
				this.$message.error('负责人不能为空');
				return
			}








			if( 'yunyingfang' ==this.flag && this.ruleForm.shoujihao&&(!isMobile(this.ruleForm.shoujihao))){
				this.$message.error(`手机号应输入手机格式`);
				return
			}




			if(this.ruleForm.touxiang!=null) {
				this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,"g"),"");
			}
			if((!this.ruleForm.jianguanzhanghao)&& 'jianguanrenyuan'==this.flag){
				this.$message.error('监管账号不能为空');
				return
			}


			if((!this.ruleForm.mima)&& 'jianguanrenyuan'==this.flag){
				this.$message.error('密码不能为空');
				return
			}



			if(this.ruleForm.touxiang!=null) {
				this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,"g"),"");
			}
			if((!this.ruleForm.jianguanxingming)&& 'jianguanrenyuan'==this.flag){
				this.$message.error('监管姓名不能为空');
				return
			}






			if( 'jianguanrenyuan' ==this.flag && this.ruleForm.jianguandianhua&&(!isMobile(this.ruleForm.jianguandianhua))){
				this.$message.error(`监管电话应输入手机格式`);
				return
			}
			if((!this.ruleForm.zhanghao)&& 'yonghu'==this.flag){
				this.$message.error('账号不能为空');
				return
			}


			if((!this.ruleForm.mima)&& 'yonghu'==this.flag){
				this.$message.error('密码不能为空');
				return
			}







			if( 'yonghu' ==this.flag && this.ruleForm.shouji&&(!isMobile(this.ruleForm.shouji))){
				this.$message.error(`手机应输入手机格式`);
				return
			}






			if(this.ruleForm.touxiang!=null) {
				this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,"g"),"");
			}
			if('users'==this.flag && this.ruleForm.username.trim().length<1) {
				this.$message.error(`用户名不能为空`);
				return	
			}
			if(this.flag=='users'){
				this.ruleForm.image = this.ruleForm.image.replace(new RegExp(this.$base.url,"g"),"")
			}
			this.$http({
				url: `${this.$storage.get("sessionTable")}/update`,
				method: "post",
				data: this.ruleForm
			}).then(({ data }) => {
				if (data && data.code === 0) {
					this.$message({
						message: "修改信息成功",
						type: "success",
						duration: 1500,
						onClose: () => {
							if(this.flag=='users'){
								this.$storage.set('headportrait',this.ruleForm.image)
							}
						}
					});
				} else {
					this.$message.error(data.msg);
				}
			});
		}
	}
};
</script>
<style lang="scss" scoped>
	.el-date-editor.el-input {
		width: auto;
	}
	
	.add-update-preview .el-form-item /deep/ .el-form-item__label {
				padding: 0 10px 0 0;
				color: #666;
				font-weight: 600;
				width: 120px;
				font-size: 16px;
				line-height: 40px;
				text-align: right;
			}
	
	.add-update-preview .el-form-item /deep/ .el-form-item__content {
		margin-left: 120px;
	}
	
	.add-update-preview .el-input /deep/ .el-input__inner {
				border: 1px dashed #ababab;
				border-radius: 0px;
				padding: 0 12px;
				color: #000000;
				width: 100%;
				font-size: 16px;
				height: 34px;
			}
	
	.add-update-preview .el-select /deep/ .el-input__inner {
				border: 1px dashed #ababab;
				border-radius: 0px;
				padding: 0 12px;
				color: #000000;
				width: 100%;
				font-size: 16px;
				height: 34px;
			}
	
	.add-update-preview .el-date-editor /deep/ .el-input__inner {
				border: 1px solid #E8E8E8;
				border-radius: 0px;
				padding: 0 30px;
				color: #000000;
				width: 100%;
				font-size: 16px;
				height: 34px;
			}
	
	.add-update-preview /deep/ .el-upload--picture-card {
		background: transparent;
		border: 0;
		border-radius: 0;
		width: auto;
		height: auto;
		line-height: initial;
		vertical-align: middle;
	}
	
	.add-update-preview /deep/ .el-upload-list .el-upload-list__item {
				border: 1px solid #ddd;
				cursor: pointer;
				border-radius: 0px;
				margin: 0 280px 0 0;
				color: #000000;
				background: #fff;
				width: 90px;
				font-size: 24px;
				line-height: 60px;
				text-align: center;
				height: 60px;
			}
	
	.add-update-preview /deep/ .el-upload .el-icon-plus {
				border: 1px solid #ddd;
				cursor: pointer;
				border-radius: 0px;
				margin: 0 280px 0 0;
				color: #000000;
				background: #fff;
				width: 90px;
				font-size: 24px;
				line-height: 60px;
				text-align: center;
				height: 60px;
			}
	
	.add-update-preview .el-textarea /deep/ .el-textarea__inner {
				border: 1px solid #E8E8E8;
				border-radius: 0px;
				padding: 0 25px;
				color: #000000;
				width: 360px;
				font-size: 16px;
				line-height: 34px;
				height: 34px;
			}
	
	.add-update-preview .btn3 {
				border: 0px solid #ccc;
				cursor: pointer;
				border-radius: 4px;
				padding: 0 10px;
				margin: 0 10px 0 0;
				color: #fff;
				background: #F5C5A9;
				width: auto;
				font-size: 16px;
				min-width: 110px;
				height: 40px;
			}
	
	.add-update-preview .btn3:hover {
				opacity: 0.8;
			}
	
	.editor>.avatar-uploader {
		line-height: 0;
		height: 0;
	}
</style>
