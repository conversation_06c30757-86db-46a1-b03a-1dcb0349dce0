<template>
	<div class="addEdit-block">
		<el-form
			class="add-update-preview"
			ref="ruleForm"
			:model="ruleForm"
			:rules="rules"
			label-width="120px"
		>
			<template >
				<el-form-item class="input" v-if="type!='info'"  label="设施编号" prop="ssbh" >
					<el-input v-model="ruleForm.ssbh" placeholder="设施编号" clearable  :readonly="ro.ssbh"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="设施编号" prop="ssbh" >
					<el-input v-model="ruleForm.ssbh" placeholder="设施编号" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="设施名称" prop="ssmc" >
					<el-input v-model="ruleForm.ssmc" placeholder="设施名称" clearable  :readonly="ro.ssmc"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="设施名称" prop="ssmc" >
					<el-input v-model="ruleForm.ssmc" placeholder="设施名称" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="适用年龄" prop="synl" >
					<el-input v-model="ruleForm.synl" placeholder="适用年龄" clearable  :readonly="ro.synl"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="适用年龄" prop="synl" >
					<el-input v-model="ruleForm.synl" placeholder="适用年龄" readonly></el-input>
				</el-form-item>
				<el-form-item class="select" v-if="type!='info'"  label="设施状态" prop="sszt" >
					<el-select :disabled="ro.sszt" v-model="ruleForm.sszt" placeholder="请选择设施状态" >
						<el-option
							v-for="(item,index) in ssztOptions"
							v-bind:key="index"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item v-else class="input" label="设施状态" prop="sszt" >
					<el-input v-model="ruleForm.sszt"
						placeholder="设施状态" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="使用频率" prop="sypl" >
					<el-input v-model="ruleForm.sypl" placeholder="使用频率" clearable  :readonly="ro.sypl"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="使用频率" prop="sypl" >
					<el-input v-model="ruleForm.sypl" placeholder="使用频率" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="使用时长" prop="sysc" >
					<el-input v-model="ruleForm.sysc" placeholder="使用时长" clearable  :readonly="ro.sysc"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="使用时长" prop="sysc" >
					<el-input v-model="ruleForm.sysc" placeholder="使用时长" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="天气状况" prop="tqzk" >
					<el-input v-model="ruleForm.tqzk" placeholder="天气状况" clearable  :readonly="ro.tqzk"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="天气状况" prop="tqzk" >
					<el-input v-model="ruleForm.tqzk" placeholder="天气状况" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="安全隐患" prop="aqyh" >
					<el-input v-model="ruleForm.aqyh" placeholder="安全隐患" clearable  :readonly="ro.aqyh"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="安全隐患" prop="aqyh" >
					<el-input v-model="ruleForm.aqyh" placeholder="安全隐患" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="安全等级" prop="aqdj" >
					<el-input v-model.number="ruleForm.aqdj" placeholder="安全等级" clearable  :readonly="ro.aqdj"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="安全等级" prop="aqdj" >
					<el-input v-model="ruleForm.aqdj" placeholder="安全等级" readonly></el-input>
				</el-form-item>
				<el-form-item class="input" v-if="type!='info'"  label="改进建议" prop="gjjy" >
					<el-input v-model="ruleForm.gjjy" placeholder="改进建议" clearable  :readonly="ro.gjjy"></el-input>
				</el-form-item>
				<el-form-item v-else class="input" label="改进建议" prop="gjjy" >
					<el-input v-model="ruleForm.gjjy" placeholder="改进建议" readonly></el-input>
				</el-form-item>
				<el-form-item class="upload" v-if="type!='info' && !ro.sstp" label="设施照片" prop="sstp" >
					<file-upload
						tip="点击上传设施照片"
						action="file/upload"
						:limit="3"
						:multiple="true"
						:fileUrls="ruleForm.sstp?ruleForm.sstp:''"
						@change="sstpUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item class="upload" v-else-if="ruleForm.sstp" label="设施照片" prop="sstp" >
					<img v-if="ruleForm.sstp.substring(0,4)=='http'&&ruleForm.sstp.split(',w').length>1" class="upload-img" style="margin-right:20px;" v-bind:key="index" :src="ruleForm.sstp" width="100" height="100">
					<img v-else-if="ruleForm.sstp.substring(0,4)=='http'" class="upload-img" style="margin-right:20px;" v-bind:key="index" :src="ruleForm.sstp.split(',')[0]" width="100" height="100">
					<img v-else class="upload-img" style="margin-right:20px;" v-bind:key="index" v-for="(item,index) in ruleForm.sstp.split(',')" :src="$base.url+item" width="100" height="100">
				</el-form-item>
			</template>
			<el-form-item class="btn">
				<el-button class="btn3"  v-if="type!='info'" type="success" @click="onSubmit">
					<span class="icon iconfont icon-xihuan"></span>
					提交
				</el-button>
				<el-button class="btn4" v-if="type!='info'" type="success" @click="back()">
					<span class="icon iconfont icon-xihuan"></span>
					取消
				</el-button>
				<el-button class="btn5" v-if="type=='info'" type="success" @click="back()">
					<span class="icon iconfont icon-xihuan"></span>
					返回
				</el-button>
			</el-form-item>
		</el-form>
    

	</div>
</template>
<script>
	import { 
		isIntNumer,
	} from "@/utils/validate";
	export default {
		data() {
			var validateIntNumber = (rule, value, callback) => {
				if(!value){
					callback();
				} else if (!isIntNumer(value)) {
					callback(new Error("请输入整数"));
				} else {
					callback();
				}
			};
			return {
				id: '',
				type: '',
			
			
				ro:{
					ssbh : false,
					ssmc : false,
					synl : false,
					sszt : false,
					sypl : false,
					sysc : false,
					tqzk : false,
					aqyh : false,
					aqdj : false,
					gjjy : false,
					sstp : false,
					thumbsupnum : false,
					crazilynum : false,
					discussnum : false,
					storeupnum : false,
				},
			
				ruleForm: {
					ssbh: '',
					ssmc: '',
					synl: '',
					sszt: '',
					sypl: '',
					sysc: '',
					tqzk: '',
					aqyh: '',
					aqdj: '',
					gjjy: '',
					sstp: '',
				},
				ssztOptions: [],

				rules: {
					ssbh: [
					],
					ssmc: [
					],
					synl: [
					],
					sszt: [
					],
					sypl: [
					],
					sysc: [
					],
					tqzk: [
					],
					aqyh: [
					],
					aqdj: [
						{ validator: validateIntNumber, trigger: 'blur' },
					],
					gjjy: [
					],
					sstp: [
					],
					thumbsupnum: [
						{ validator: validateIntNumber, trigger: 'blur' },
					],
					crazilynum: [
						{ validator: validateIntNumber, trigger: 'blur' },
					],
					discussnum: [
						{ validator: validateIntNumber, trigger: 'blur' },
					],
					storeupnum: [
						{ validator: validateIntNumber, trigger: 'blur' },
					],
				},
			};
		},
		props: ["parent"],
		computed: {



		},
		components: {
		},
		created() {
		},
		methods: {
			// 下载
			download(file){
				window.open(`${file}`)
			},
			// 初始化
			init(id,type) {
				if (id) {
					this.id = id;
					this.type = type;
				}
				if(this.type=='info'||this.type=='else'||this.type=='msg'){
					this.info(id);
				}else if(this.type=='logistics'){
					for(let x in this.ro) {
						this.ro[x] = true
					}
					this.logistics=false;
					this.info(id);
				}else if(this.type=='cross'){
					var obj = this.$storage.getObj('crossObj');
					for (var o in obj){
						if(o=='ssbh'){
							this.ruleForm.ssbh = obj[o];
							this.ro.ssbh = true;
							continue;
						}
						if(o=='ssmc'){
							this.ruleForm.ssmc = obj[o];
							this.ro.ssmc = true;
							continue;
						}
						if(o=='synl'){
							this.ruleForm.synl = obj[o];
							this.ro.synl = true;
							continue;
						}
						if(o=='sszt'){
							this.ruleForm.sszt = obj[o];
							this.ro.sszt = true;
							continue;
						}
						if(o=='sypl'){
							this.ruleForm.sypl = obj[o];
							this.ro.sypl = true;
							continue;
						}
						if(o=='sysc'){
							this.ruleForm.sysc = obj[o];
							this.ro.sysc = true;
							continue;
						}
						if(o=='tqzk'){
							this.ruleForm.tqzk = obj[o];
							this.ro.tqzk = true;
							continue;
						}
						if(o=='aqyh'){
							this.ruleForm.aqyh = obj[o];
							this.ro.aqyh = true;
							continue;
						}
						if(o=='aqdj'){
							this.ruleForm.aqdj = obj[o];
							this.ro.aqdj = true;
							continue;
						}
						if(o=='gjjy'){
							this.ruleForm.gjjy = obj[o];
							this.ro.gjjy = true;
							continue;
						}
						if(o=='sstp'){
							this.ruleForm.sstp = obj[o];
							this.ro.sstp = true;
							continue;
						}
						if(o=='thumbsupnum'){
							this.ruleForm.thumbsupnum = obj[o];
							this.ro.thumbsupnum = true;
							continue;
						}
						if(o=='crazilynum'){
							this.ruleForm.crazilynum = obj[o];
							this.ro.crazilynum = true;
							continue;
						}
						if(o=='discussnum'){
							this.ruleForm.discussnum = obj[o];
							this.ro.discussnum = true;
							continue;
						}
						if(o=='storeupnum'){
							this.ruleForm.storeupnum = obj[o];
							this.ro.storeupnum = true;
							continue;
						}
					}
				}
				// 获取用户信息
				this.$http({
					url: `${this.$storage.get('sessionTable')}/session`,
					method: "get"
				}).then(({ data }) => {
					if (data && data.code === 0) {
						var json = data.data;
					} else {
						this.$message.error(data.msg);
					}
				});
				this.ssztOptions = "运行,正常,维护".split(',')
			
			},
			// 多级联动参数

			info(id) {
				this.$http({
					url: `facilities/info/${id}`,
					method: "get"
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.ruleForm = data.data;
						//解决前台上传图片后台不显示的问题
						let reg=new RegExp('../../../upload','g')//g代表全部
					} else {
						this.$message.error(data.msg);
					}
				});
			},

			// 提交
			async onSubmit() {
					if(this.ruleForm.sstp!=null) {
						this.ruleForm.sstp = this.ruleForm.sstp.replace(new RegExp(this.$base.url,"g"),"");
					}
					var objcross = this.$storage.getObj('crossObj');
					await this.$refs["ruleForm"].validate(async valid => {
						if (valid) {
							if(this.type=='cross'){
								var statusColumnName = this.$storage.get('statusColumnName');
								var statusColumnValue = this.$storage.get('statusColumnValue');
								if(statusColumnName!='') {
									var obj = this.$storage.getObj('crossObj');
									if(statusColumnName && !statusColumnName.startsWith("[")) {
										for (var o in obj){
											if(o==statusColumnName){
												obj[o] = statusColumnValue;
											}
										}
										var table = this.$storage.get('crossTable');
										await this.$http({
											url: `${table}/update`,
											method: "post",
											data: obj
										}).then(({ data }) => {});
									}
								}
							}
							
							await this.$http({
								url: `facilities/${!this.ruleForm.id ? "save" : "update"}`,
								method: "post",
								data: this.ruleForm
							}).then(async ({ data }) => {
								if (data && data.code === 0) {
									this.$message({
										message: "操作成功",
										type: "success",
										duration: 1500,
										onClose: () => {
											this.parent.showFlag = true;
											this.parent.addOrUpdateFlag = false;
											this.parent.facilitiesCrossAddOrUpdateFlag = false;
											this.parent.search();
											this.parent.contentStyleChange();
										}
									});
								} else {
									this.$message.error(data.msg);
								}
							});
						}
					});
			},
			// 获取uuid
			getUUID () {
				return new Date().getTime();
			},
			// 返回
			back() {
				this.parent.showFlag = true;
				this.parent.addOrUpdateFlag = false;
				this.parent.facilitiesCrossAddOrUpdateFlag = false;
				this.parent.contentStyleChange();
			},
			sstpUploadChange(fileUrls) {
				this.ruleForm.sstp = fileUrls;
			},
		}
	};
</script>
<style lang="scss" scoped>
	.addEdit-block {
		padding: 20px 30px;
	}
	.add-update-preview {
		border: 1px solid #BFBFBF;
		padding: 40px 20% 20px 15%;
		background: #fff;
	}
	.amap-wrapper {
		width: 100%;
		height: 500px;
	}
	
	.search-box {
		position: absolute;
	}
	
	.el-date-editor.el-input {
		width: auto;
	}
	.add-update-preview /deep/ .el-form-item {
		border: 0px solid #eee;
		padding: 0;
		margin: 0 0 20px 0;
		display: inline-block;
		width: 80%;
	}
	.add-update-preview .el-form-item /deep/ .el-form-item__label {
		padding: 0 10px 0 0;
		color: #666;
		font-weight: 600;
		width: 120px;
		font-size: 16px;
		line-height: 40px;
		text-align: right;
	}
	
	.add-update-preview .el-form-item /deep/ .el-form-item__content {
		margin-left: 120px;
	}
	.add-update-preview .el-form-item span.text {
		border: 1px solid #ddd;
		border-radius: 0px;
		padding: 5px 10px;
		color: #000000;
		display: block;
		width: 100%;
		font-size: 16px;
		line-height: 24px;
		height: auto;
	}
	
	.add-update-preview .el-input {
		width: 100%;
	}
	.add-update-preview .el-input /deep/ .el-input__inner {
		border: 1px dashed #ababab;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-input /deep/ .el-input__inner[readonly="readonly"] {
		border: 1px dashed #ababab;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-input-number {
		text-align: left;
		width: 100%;
	}
	.add-update-preview .el-input-number /deep/ .el-input__inner {
		text-align: left;
		border: 1px dashed #ababab;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-input-number /deep/ .is-disabled .el-input__inner {
		text-align: left;
		border: 1px dashed #ababab;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-input-number /deep/ .el-input-number__decrease {
		display: none;
	}
	.add-update-preview .el-input-number /deep/ .el-input-number__increase {
		display: none;
	}
	.add-update-preview .el-select {
		width: 100%;
	}
	.add-update-preview .el-select /deep/ .el-input__inner {
		border: 1px dashed #ababab;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-select /deep/ .is-disabled .el-input__inner {
		border: 1px solid #E8E8E8;
		border-radius: 0px;
		padding: 0 12px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-date-editor {
		width: 100%;
	}
	.add-update-preview .el-date-editor /deep/ .el-input__inner {
		border: 1px solid #E8E8E8;
		border-radius: 0px;
		padding: 0 30px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .el-date-editor /deep/ .el-input__inner[readonly="readonly"] {
		border: 1px solid #E8E8E8;
		border-radius: 0px;
		padding: 0 30px;
		color: #000000;
		width: 100%;
		font-size: 16px;
		height: 34px;
	}
	.add-update-preview .viewBtn {
		border: 0px solid #ccc;
		cursor: pointer;
		border-radius: 0px;
		padding: 0 15px;
		margin: 0 20px 0 0;
		color: #666;
		background: #fff;
		width: auto;
		font-size: 15px;
		line-height: 34px;
		height: 34px;
		.iconfont {
			margin: 0 2px;
			color: #666;
			font-size: 16px;
			height: 34px;
		}
	}
	.add-update-preview .viewBtn:hover {
		opacity: 0.8;
	}
	.add-update-preview .downBtn {
		border: 0px solid #ccc;
		cursor: pointer;
		border-radius: 0px;
		padding: 0 15px;
		margin: 0 20px 0 0;
		color: #666;
		background: #fff;
		width: auto;
		font-size: 15px;
		line-height: 34px;
		height: 34px;
		.iconfont {
			margin: 0 2px;
			color: #666;
			font-size: 16px;
			height: 34px;
		}
	}
	.add-update-preview .downBtn:hover {
		opacity: 0.8;
	}
	.add-update-preview .unBtn {
		border: 0;
		cursor: not-allowed;
		border-radius: 4px;
		padding: 0 0px;
		margin: 0 20px 0 0;
		outline: none;
		color: #999;
		background: none;
		width: auto;
		font-size: 16px;
		line-height: 40px;
		height: 40px;
		.iconfont {
			margin: 0 2px;
			color: #fff;
			display: none;
			font-size: 14px;
			height: 34px;
		}
	}
	.add-update-preview .unBtn:hover {
		opacity: 0.8;
	}
	.add-update-preview /deep/ .el-upload--picture-card {
		background: transparent;
		border: 0;
		border-radius: 0;
		width: auto;
		height: auto;
		line-height: initial;
		vertical-align: middle;
	}
	
	.add-update-preview /deep/ .upload .upload-img {
		border: 1px solid #ddd;
		cursor: pointer;
		border-radius: 0px;
		margin: 0 280px 0 0;
		color: #000000;
		background: #fff;
		width: 90px;
		font-size: 24px;
		line-height: 60px;
		text-align: center;
		height: 60px;
	}
	
	.add-update-preview /deep/ .el-upload-list .el-upload-list__item {
		border: 1px solid #ddd;
		cursor: pointer;
		border-radius: 0px;
		margin: 0 280px 0 0;
		color: #000000;
		background: #fff;
		width: 90px;
		font-size: 24px;
		line-height: 60px;
		text-align: center;
		height: 60px;
	}
	
	.add-update-preview /deep/ .el-upload .el-icon-plus {
		border: 1px solid #ddd;
		cursor: pointer;
		border-radius: 0px;
		margin: 0 280px 0 0;
		color: #000000;
		background: #fff;
		width: 90px;
		font-size: 24px;
		line-height: 60px;
		text-align: center;
		height: 60px;
	}
	.add-update-preview /deep/ .el-upload__tip {
		color: #666;
		font-size: 15px;
	}
	
	.add-update-preview .el-textarea /deep/ .el-textarea__inner {
		border: 1px solid #E8E8E8;
		border-radius: 0px;
		padding: 0 25px;
		color: #000000;
		width: 360px;
		font-size: 16px;
		line-height: 34px;
		height: 34px;
	}
	.add-update-preview .el-textarea /deep/ .el-textarea__inner[readonly="readonly"] {
				border: 1px solid #E8E8E8;
				border-radius: 0px;
				padding: 0 25px;
				color: #000000;
				width: 360px;
				font-size: 16px;
				line-height: 34px;
				height: 34px;
			}
	.add-update-preview .el-form-item.btn {
		padding: 0;
		margin: 20px 0 0;
		.btn1 {
			border: 0px solid #ccc;
			cursor: pointer;
			border-radius: 4px;
			padding: 0 10px;
			margin: 0 10px 0 0;
			color: #fff;
			background: #6DB344;
			width: auto;
			font-size: 16px;
			min-width: 110px;
			height: 40px;
			.iconfont {
				margin: 0 2px;
				color: #fff;
				display: none;
				font-size: 14px;
				height: 40px;
			}
		}
		.btn1:hover {
			opacity: 0.8;
		}
		.btn2 {
			border: 0px solid #ccc;
			cursor: pointer;
			border-radius: 4px;
			padding: 0 10px;
			margin: 0 10px 0 0;
			color: #fff;
			background: #37A3D1;
			width: auto;
			font-size: 16px;
			min-width: 110px;
			height: 40px;
			.iconfont {
				margin: 0 2px;
				color: #fff;
				display: none;
				font-size: 14px;
				height: 34px;
			}
		}
		.btn2:hover {
			opacity: 0.8;
		}
		.btn3 {
			border: 0px solid #ccc;
			cursor: pointer;
			border-radius: 4px;
			padding: 0 10px;
			margin: 0 10px 0 0;
			color: #fff;
			background: #F5C5A9;
			width: auto;
			font-size: 16px;
			min-width: 110px;
			height: 40px;
			.iconfont {
				margin: 0 2px;
				color: #fff;
				display: none;
				font-size: 14px;
				height: 40px;
			}
		}
		.btn3:hover {
			opacity: 0.8;
		}
		.btn4 {
			border: 0px solid #ccc;
			cursor: pointer;
			border-radius: 4px;
			padding: 0 10px;
			margin: 0 10px 0 0;
			color: #fff;
			background: #ADADAC;
			width: auto;
			font-size: 16px;
			min-width: 110px;
			height: 40px;
			.iconfont {
				margin: 0 2px;
				color: #fff;
				display: none;
				font-size: 14px;
				height: 40px;
			}
		}
		.btn4:hover {
			opacity: 0.8;
		}
		.btn5 {
			border: 0px solid #ccc;
			cursor: pointer;
			border-radius: 4px;
			padding: 0 10px;
			margin: 0 10px 0 0;
			color: #fff;
			background: #FFBB33;
			width: auto;
			font-size: 16px;
			min-width: 110px;
			height: 40px;
			.iconfont {
				margin: 0 2px;
				color: #fff;
				display: none;
				font-size: 14px;
				height: 40px;
			}
		}
		.btn5:hover {
			opacity: 0.8;
		}
	}
</style>
