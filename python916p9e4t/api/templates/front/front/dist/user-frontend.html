<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>儿童游乐设施安全评估系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-logo {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }

        .nav-auth {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .btn-primary {
            background-color: #ff6b6b;
            color: white;
        }

        .btn-outline {
            background-color: transparent;
            color: white;
            border: 1px solid white;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 主要内容区域 */
        .main-content {
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        .content-section {
            display: none;
            padding: 2rem 0;
        }

        .content-section.active {
            display: block;
        }

        /* 首页样式 */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero-content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* 功能卡片 */
        .features-section {
            padding: 4rem 0;
            background: white;
        }

        .features-section h2 {
            text-align: center;
            margin-bottom: 3rem;
            font-size: 2.5rem;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* 卡片网格布局 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-3px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .card-meta {
            font-size: 0.9rem;
            color: #666;
        }

        .card-content {
            margin-bottom: 1rem;
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        /* 搜索和筛选 */
        .search-filter {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .filter-select {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            min-width: 120px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 2000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        /* 安全等级标签 */
        .safety-level {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .safety-level-safe {
            background-color: #d4edda;
            color: #155724;
        }

        .safety-level-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .safety-level-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 状态标签 */
        .status-tag {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-running {
            background-color: #d4edda;
            color: #155724;
        }

        .status-maintenance {
            background-color: #fff3cd;
            color: #856404;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-content h1 {
                font-size: 2rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .search-row {
                flex-direction: column;
            }

            .search-input, .filter-select {
                width: 100%;
            }
        }

        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 100px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            color: white;
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s;
        }

        .message.show {
            transform: translateX(0);
        }

        .message.success {
            background-color: #28a745;
        }

        .message.error {
            background-color: #dc3545;
        }

        .message.info {
            background-color: #17a2b8;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-shield-alt"></i>
                <span>儿童游乐设施安全评估系统</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a class="nav-link active" data-section="home">
                        <i class="fas fa-home"></i> 首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="facilities">
                        <i class="fas fa-playground"></i> 设施信息
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="safety">
                        <i class="fas fa-book"></i> 安全规范
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="accidents">
                        <i class="fas fa-exclamation-triangle"></i> 事故记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="feedback">
                        <i class="fas fa-comment"></i> 反馈意见
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="discussion">
                        <i class="fas fa-comments"></i> 安全讨论
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-section="bookmarks">
                        <i class="fas fa-bookmark"></i> 我的收藏
                    </a>
                </li>
            </ul>
            <div class="nav-auth">
                <div id="user-info" class="user-info" style="display: none;">
                    <img id="user-avatar" src="img/avator.c58e4651.png" alt="头像" class="user-avatar">
                    <span id="user-name"></span>
                    <button id="logout-btn" class="btn btn-outline btn-small">退出</button>
                </div>
                <div id="auth-buttons" class="auth-buttons">
                    <button id="login-btn" class="btn btn-outline">登录</button>
                    <button id="register-btn" class="btn btn-primary">注册</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 首页 -->
        <section id="home-section" class="content-section active">
            <div class="hero-section">
                <div class="hero-content">
                    <h1>儿童游乐设施安全评估系统</h1>
                    <p>基于大数据技术的智能化儿童游乐设施安全性评估与管理平台</p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="total-facilities">0</div>
                            <div class="stat-label">设施总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="safe-facilities">0</div>
                            <div class="stat-label">安全设施</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="total-users">0</div>
                            <div class="stat-label">注册用户</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <div class="container">
                    <h2>系统功能</h2>
                    <div class="features-grid">
                        <div class="feature-card" data-section="facilities">
                            <i class="fas fa-playground"></i>
                            <h3>设施信息查询</h3>
                            <p>查看游乐设施详细信息、安全等级、使用状况等</p>
                        </div>
                        <div class="feature-card" data-section="safety">
                            <i class="fas fa-shield-alt"></i>
                            <h3>安全规范浏览</h3>
                            <p>了解最新的安全规范、操作指南和合规要求</p>
                        </div>
                        <div class="feature-card" data-section="accidents">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>事故记录查询</h3>
                            <p>查看历史事故记录，学习安全经验教训</p>
                        </div>
                        <div class="feature-card" data-section="feedback">
                            <i class="fas fa-comment-dots"></i>
                            <h3>反馈意见提交</h3>
                            <p>提交安全建议、问题反馈和改进意见</p>
                        </div>
                        <div class="feature-card" data-section="discussion">
                            <i class="fas fa-users"></i>
                            <h3>安全讨论参与</h3>
                            <p>参与安全话题讨论，分享经验和见解</p>
                        </div>
                        <div class="feature-card" data-section="bookmarks">
                            <i class="fas fa-bookmark"></i>
                            <h3>重要信息收藏</h3>
                            <p>收藏重要的安全信息和设施资料</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 设施信息 -->
        <section id="facilities-section" class="content-section">
            <div class="container">
                <h2>设施信息查询</h2>

                <div class="search-filter">
                    <div class="search-row">
                        <input type="text" id="facility-search" class="search-input" placeholder="搜索设施名称...">
                        <select id="facility-type-filter" class="filter-select">
                            <option value="">所有类型</option>
                            <option value="滑梯">滑梯</option>
                            <option value="秋千">秋千</option>
                            <option value="跷跷板">跷跷板</option>
                            <option value="攀爬架">攀爬架</option>
                            <option value="转椅">转椅</option>
                        </select>
                        <select id="safety-level-filter" class="filter-select">
                            <option value="">所有安全等级</option>
                            <option value="安全">安全</option>
                            <option value="警告">警告</option>
                            <option value="危险">危险</option>
                        </select>
                        <button id="search-facilities" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="facilities-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="facilities-grid" class="card-grid">
                    <!-- 设施卡片将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 安全规范 -->
        <section id="safety-section" class="content-section">
            <div class="container">
                <h2>安全规范浏览</h2>

                <div class="search-filter">
                    <div class="search-row">
                        <input type="text" id="safety-search" class="search-input" placeholder="搜索规范标题...">
                        <select id="safety-category-filter" class="filter-select">
                            <option value="">所有分类</option>
                            <option value="国家标准">国家标准</option>
                            <option value="行业规范">行业规范</option>
                            <option value="操作指南">操作指南</option>
                            <option value="维护标准">维护标准</option>
                        </select>
                        <button id="search-safety" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="safety-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="safety-grid" class="card-grid">
                    <!-- 安全规范卡片将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 事故记录 -->
        <section id="accidents-section" class="content-section">
            <div class="container">
                <h2>事故记录查询</h2>

                <div class="search-filter">
                    <div class="search-row">
                        <input type="text" id="accident-search" class="search-input" placeholder="搜索事故描述...">
                        <select id="accident-level-filter" class="filter-select">
                            <option value="">所有严重程度</option>
                            <option value="轻微">轻微</option>
                            <option value="一般">一般</option>
                            <option value="严重">严重</option>
                            <option value="重大">重大</option>
                        </select>
                        <input type="date" id="accident-date-from" class="filter-select">
                        <input type="date" id="accident-date-to" class="filter-select">
                        <button id="search-accidents" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="accidents-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="accidents-grid" class="card-grid">
                    <!-- 事故记录卡片将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 反馈意见 -->
        <section id="feedback-section" class="content-section">
            <div class="container">
                <h2>反馈意见提交</h2>

                <div class="card">
                    <h3>提交新的反馈意见</h3>
                    <form id="feedback-form">
                        <div class="form-group">
                            <label class="form-label">反馈类型</label>
                            <select id="feedback-type" class="form-input" required>
                                <option value="">请选择反馈类型</option>
                                <option value="安全问题">安全问题</option>
                                <option value="设施故障">设施故障</option>
                                <option value="改进建议">改进建议</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">相关设施</label>
                            <select id="feedback-facility" class="form-input">
                                <option value="">请选择相关设施（可选）</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">反馈标题</label>
                            <input type="text" id="feedback-title" class="form-input" placeholder="请输入反馈标题" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">详细描述</label>
                            <textarea id="feedback-content" class="form-textarea" placeholder="请详细描述您的反馈意见..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">联系方式（可选）</label>
                            <input type="text" id="feedback-contact" class="form-input" placeholder="手机号或邮箱">
                        </div>
                        <button type="submit" class="btn btn-primary">提交反馈</button>
                    </form>
                </div>

                <div class="search-filter">
                    <h3>我的反馈记录</h3>
                    <div class="search-row">
                        <input type="text" id="my-feedback-search" class="search-input" placeholder="搜索我的反馈...">
                        <select id="my-feedback-status-filter" class="filter-select">
                            <option value="">所有状态</option>
                            <option value="待处理">待处理</option>
                            <option value="处理中">处理中</option>
                            <option value="已解决">已解决</option>
                            <option value="已关闭">已关闭</option>
                        </select>
                        <button id="search-my-feedback" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="my-feedback-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="my-feedback-grid" class="card-grid">
                    <!-- 我的反馈记录将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 安全讨论 -->
        <section id="discussion-section" class="content-section">
            <div class="container">
                <h2>安全讨论</h2>

                <div class="card">
                    <h3>发起新讨论</h3>
                    <form id="discussion-form">
                        <div class="form-group">
                            <label class="form-label">讨论主题</label>
                            <input type="text" id="discussion-title" class="form-input" placeholder="请输入讨论主题" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">相关设施</label>
                            <select id="discussion-facility" class="form-input">
                                <option value="">请选择相关设施（可选）</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">讨论内容</label>
                            <textarea id="discussion-content" class="form-textarea" placeholder="请输入讨论内容..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">发起讨论</button>
                    </form>
                </div>

                <div class="search-filter">
                    <div class="search-row">
                        <input type="text" id="discussion-search" class="search-input" placeholder="搜索讨论主题...">
                        <select id="discussion-facility-filter" class="filter-select">
                            <option value="">所有设施</option>
                        </select>
                        <select id="discussion-sort" class="filter-select">
                            <option value="latest">最新发布</option>
                            <option value="popular">最多回复</option>
                            <option value="active">最近活跃</option>
                        </select>
                        <button id="search-discussions" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="discussions-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="discussions-grid" class="card-grid">
                    <!-- 讨论列表将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 我的收藏 -->
        <section id="bookmarks-section" class="content-section">
            <div class="container">
                <h2>我的收藏</h2>

                <div class="search-filter">
                    <div class="search-row">
                        <input type="text" id="bookmark-search" class="search-input" placeholder="搜索收藏内容...">
                        <select id="bookmark-type-filter" class="filter-select">
                            <option value="">所有类型</option>
                            <option value="设施信息">设施信息</option>
                            <option value="安全规范">安全规范</option>
                            <option value="事故记录">事故记录</option>
                            <option value="讨论话题">讨论话题</option>
                        </select>
                        <button id="search-bookmarks" class="btn btn-primary">搜索</button>
                    </div>
                </div>

                <div id="bookmarks-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>

                <div id="bookmarks-grid" class="card-grid">
                    <!-- 收藏列表将在这里动态生成 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 登录模态框 -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">用户登录</h3>
                <button class="close-btn" onclick="closeModal('login-modal')">&times;</button>
            </div>
            <form id="login-form">
                <div class="form-group">
                    <label class="form-label">账号</label>
                    <input type="text" id="login-username" class="form-input" placeholder="请输入账号" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" id="login-password" class="form-input" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">登录</button>
            </form>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div id="register-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">用户注册</h3>
                <button class="close-btn" onclick="closeModal('register-modal')">&times;</button>
            </div>
            <form id="register-form">
                <div class="form-group">
                    <label class="form-label">账号</label>
                    <input type="text" id="register-username" class="form-input" placeholder="请输入账号" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" id="register-password" class="form-input" placeholder="请输入密码" required>
                </div>
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" id="register-confirm-password" class="form-input" placeholder="请确认密码" required>
                </div>
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" id="register-name" class="form-input" placeholder="请输入姓名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">性别</label>
                    <select id="register-gender" class="form-input" required>
                        <option value="">请选择性别</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" id="register-phone" class="form-input" placeholder="请输入手机号">
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" id="register-email" class="form-input" placeholder="请输入邮箱">
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">注册</button>
            </form>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detail-modal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 id="detail-modal-title" class="modal-title"></h3>
                <button class="close-btn" onclick="closeModal('detail-modal')">&times;</button>
            </div>
            <div id="detail-modal-content">
                <!-- 详情内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message"></div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 全局变量
        let currentUser = null;
        let currentSection = 'home';

        // API基础URL
        const API_BASE = '/api';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
            checkUserSession();
        });

        // 初始化应用
        function initializeApp() {
            loadHomeStats();
            loadFacilitiesForSelect();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 导航菜单
            document.querySelectorAll('.nav-link, .feature-card').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    if (section) {
                        switchSection(section);
                    }
                });
            });

            // 认证按钮
            document.getElementById('login-btn').addEventListener('click', () => openModal('login-modal'));
            document.getElementById('register-btn').addEventListener('click', () => openModal('register-modal'));
            document.getElementById('logout-btn').addEventListener('click', logout);

            // 表单提交
            document.getElementById('login-form').addEventListener('submit', handleLogin);
            document.getElementById('register-form').addEventListener('submit', handleRegister);
            document.getElementById('feedback-form').addEventListener('submit', handleFeedbackSubmit);
            document.getElementById('discussion-form').addEventListener('submit', handleDiscussionSubmit);

            // 搜索按钮
            document.getElementById('search-facilities').addEventListener('click', searchFacilities);
            document.getElementById('search-safety').addEventListener('click', searchSafety);
            document.getElementById('search-accidents').addEventListener('click', searchAccidents);
            document.getElementById('search-my-feedback').addEventListener('click', searchMyFeedback);
            document.getElementById('search-discussions').addEventListener('click', searchDiscussions);
            document.getElementById('search-bookmarks').addEventListener('click', searchBookmarks);

            // 模态框点击外部关闭
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(this.id);
                    }
                });
            });
        }

        // 切换页面部分
        function switchSection(section) {
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-section="${section}"]`).classList.add('active');

            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(sec => {
                sec.classList.remove('active');
            });

            // 显示目标区域
            document.getElementById(`${section}-section`).classList.add('active');
            currentSection = section;

            // 加载对应数据
            loadSectionData(section);
        }

        // 加载页面数据
        function loadSectionData(section) {
            switch(section) {
                case 'home':
                    loadHomeStats();
                    break;
                case 'facilities':
                    searchFacilities();
                    break;
                case 'safety':
                    searchSafety();
                    break;
                case 'accidents':
                    searchAccidents();
                    break;
                case 'feedback':
                    if (currentUser) {
                        searchMyFeedback();
                    }
                    break;
                case 'discussion':
                    searchDiscussions();
                    break;
                case 'bookmarks':
                    if (currentUser) {
                        searchBookmarks();
                    }
                    break;
            }
        }

        // 检查用户会话
        async function checkUserSession() {
            const token = localStorage.getItem('userToken');
            if (token) {
                try {
                    const response = await axios.get(`${API_BASE}/yonghu/session`, {
                        headers: { 'token': token }
                    });
                    if (response.data.code === 0) {
                        currentUser = response.data.data;
                        updateUserUI();
                    } else {
                        localStorage.removeItem('userToken');
                    }
                } catch (error) {
                    console.error('Session check failed:', error);
                    localStorage.removeItem('userToken');
                }
            }
        }

        // 更新用户界面
        function updateUserUI() {
            if (currentUser) {
                document.getElementById('user-info').style.display = 'flex';
                document.getElementById('auth-buttons').style.display = 'none';
                document.getElementById('user-name').textContent = currentUser.xingming || currentUser.zhanghao;
                if (currentUser.touxiang) {
                    document.getElementById('user-avatar').src = currentUser.touxiang;
                }
            } else {
                document.getElementById('user-info').style.display = 'none';
                document.getElementById('auth-buttons').style.display = 'flex';
            }
        }

        // 登录处理
        async function handleLogin(e) {
            e.preventDefault();
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await axios.post(`${API_BASE}/yonghu/login`, {
                    zhanghao: username,
                    mima: password
                });

                if (response.data.code === 0) {
                    currentUser = response.data.data;
                    localStorage.setItem('userToken', response.data.token);
                    updateUserUI();
                    closeModal('login-modal');
                    showMessage('登录成功！', 'success');
                    document.getElementById('login-form').reset();
                } else {
                    showMessage(response.data.msg || '登录失败', 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showMessage('登录失败，请检查网络连接', 'error');
            }
        }

        // 注册处理
        async function handleRegister(e) {
            e.preventDefault();
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;

            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            const userData = {
                zhanghao: document.getElementById('register-username').value,
                mima: password,
                xingming: document.getElementById('register-name').value,
                xingbie: document.getElementById('register-gender').value,
                shouji: document.getElementById('register-phone').value,
                youxiang: document.getElementById('register-email').value
            };

            try {
                const response = await axios.post(`${API_BASE}/yonghu/register`, userData);

                if (response.data.code === 0) {
                    showMessage('注册成功！请登录', 'success');
                    closeModal('register-modal');
                    document.getElementById('register-form').reset();
                    openModal('login-modal');
                } else {
                    showMessage(response.data.msg || '注册失败', 'error');
                }
            } catch (error) {
                console.error('Register error:', error);
                showMessage('注册失败，请检查网络连接', 'error');
            }
        }

        // 退出登录
        function logout() {
            currentUser = null;
            localStorage.removeItem('userToken');
            updateUserUI();
            showMessage('已退出登录', 'info');
            switchSection('home');
        }

        // 加载首页统计数据
        async function loadHomeStats() {
            try {
                const [facilitiesRes, usersRes] = await Promise.all([
                    axios.get(`${API_BASE}/sheshi/page?page=1&limit=1`),
                    axios.get(`${API_BASE}/yonghu/page?page=1&limit=1`)
                ]);

                if (facilitiesRes.data.code === 0) {
                    const totalFacilities = facilitiesRes.data.data.total || 0;
                    document.getElementById('total-facilities').textContent = totalFacilities;
                    // 假设安全设施占80%
                    document.getElementById('safe-facilities').textContent = Math.floor(totalFacilities * 0.8);
                }

                if (usersRes.data.code === 0) {
                    document.getElementById('total-users').textContent = usersRes.data.data.total || 0;
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // 加载设施选择列表
        async function loadFacilitiesForSelect() {
            try {
                const response = await axios.get(`${API_BASE}/sheshi/page?page=1&limit=100`);
                if (response.data.code === 0) {
                    const facilities = response.data.data.list || [];
                    const selects = [
                        'feedback-facility',
                        'discussion-facility',
                        'discussion-facility-filter'
                    ];

                    selects.forEach(selectId => {
                        const select = document.getElementById(selectId);
                        if (select) {
                            // 清空现有选项（保留第一个默认选项）
                            while (select.children.length > 1) {
                                select.removeChild(select.lastChild);
                            }

                            // 添加设施选项
                            facilities.forEach(facility => {
                                const option = document.createElement('option');
                                option.value = facility.id;
                                option.textContent = facility.sheshimingcheng;
                                select.appendChild(option);
                            });
                        }
                    });
                }
            } catch (error) {
                console.error('Failed to load facilities for select:', error);
            }
        }

        // 搜索设施
        async function searchFacilities() {
            const loading = document.getElementById('facilities-loading');
            const grid = document.getElementById('facilities-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('facility-search').value;
                const typeFilter = document.getElementById('facility-type-filter').value;
                const safetyFilter = document.getElementById('safety-level-filter').value;

                let url = `${API_BASE}/sheshi/page?page=1&limit=20`;
                if (searchText) url += `&sheshimingcheng=${encodeURIComponent(searchText)}`;
                if (typeFilter) url += `&sheshileixing=${encodeURIComponent(typeFilter)}`;

                const response = await axios.get(url);

                if (response.data.code === 0) {
                    const facilities = response.data.data.list || [];
                    renderFacilities(facilities);
                } else {
                    showMessage('加载设施信息失败', 'error');
                }
            } catch (error) {
                console.error('Search facilities error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染设施列表
        function renderFacilities(facilities) {
            const grid = document.getElementById('facilities-grid');

            if (facilities.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无设施信息</p></div>';
                return;
            }

            grid.innerHTML = facilities.map(facility => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">${facility.sheshimingcheng}</div>
                        <div class="card-meta">${formatDate(facility.addtime)}</div>
                    </div>
                    <div class="card-content">
                        <p><strong>类型：</strong>${facility.sheshileixing || '未分类'}</p>
                        <p><strong>位置：</strong>${facility.weizhi || '未指定'}</p>
                        <p><strong>状态：</strong><span class="status-tag ${facility.zhuangtai === '正常运行' ? 'status-running' : 'status-maintenance'}">${facility.zhuangtai || '未知'}</span></p>
                        <p><strong>安全等级：</strong><span class="safety-level safety-level-${getSafetyLevelClass(facility.anquandengji)}">${facility.anquandengji || '未评估'}</span></p>
                        ${facility.tupian ? `<img src="${facility.tupian}" alt="设施图片" style="width: 100%; max-height: 200px; object-fit: cover; border-radius: 5px; margin-top: 10px;">` : ''}
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewFacilityDetail(${facility.id})">查看详情</button>
                        ${currentUser ? `<button class="btn btn-primary btn-small" onclick="bookmarkItem('设施信息', ${facility.id}, '${facility.sheshimingcheng}')">收藏</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 搜索安全规范
        async function searchSafety() {
            const loading = document.getElementById('safety-loading');
            const grid = document.getElementById('safety-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('safety-search').value;
                const categoryFilter = document.getElementById('safety-category-filter').value;

                let url = `${API_BASE}/anquanguifan/page?page=1&limit=20`;
                if (searchText) url += `&guifanbiaoti=${encodeURIComponent(searchText)}`;
                if (categoryFilter) url += `&guifanleixing=${encodeURIComponent(categoryFilter)}`;

                const response = await axios.get(url);

                if (response.data.code === 0) {
                    const safetyRules = response.data.data.list || [];
                    renderSafetyRules(safetyRules);
                } else {
                    showMessage('加载安全规范失败', 'error');
                }
            } catch (error) {
                console.error('Search safety rules error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染安全规范列表
        function renderSafetyRules(rules) {
            const grid = document.getElementById('safety-grid');

            if (rules.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无安全规范</p></div>';
                return;
            }

            grid.innerHTML = rules.map(rule => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">${rule.guifanbiaoti}</div>
                        <div class="card-meta">${formatDate(rule.addtime)}</div>
                    </div>
                    <div class="card-content">
                        <p><strong>类型：</strong>${rule.guifanleixing || '未分类'}</p>
                        <p><strong>发布机构：</strong>${rule.fabujigou || '未知'}</p>
                        <p><strong>生效日期：</strong>${rule.shengxiaoriqi || '未指定'}</p>
                        <p class="card-description">${truncateText(rule.guifanneirong, 100)}</p>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewSafetyDetail(${rule.id})">查看详情</button>
                        ${currentUser ? `<button class="btn btn-primary btn-small" onclick="bookmarkItem('安全规范', ${rule.id}, '${rule.guifanbiaoti}')">收藏</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 搜索事故记录
        async function searchAccidents() {
            const loading = document.getElementById('accidents-loading');
            const grid = document.getElementById('accidents-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('accident-search').value;
                const levelFilter = document.getElementById('accident-level-filter').value;
                const dateFrom = document.getElementById('accident-date-from').value;
                const dateTo = document.getElementById('accident-date-to').value;

                let url = `${API_BASE}/shigujilu/page?page=1&limit=20`;
                if (searchText) url += `&shigumiaoshu=${encodeURIComponent(searchText)}`;
                if (levelFilter) url += `&yanzhongchengdu=${encodeURIComponent(levelFilter)}`;

                const response = await axios.get(url);

                if (response.data.code === 0) {
                    const accidents = response.data.data.list || [];
                    renderAccidents(accidents);
                } else {
                    showMessage('加载事故记录失败', 'error');
                }
            } catch (error) {
                console.error('Search accidents error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染事故记录列表
        function renderAccidents(accidents) {
            const grid = document.getElementById('accidents-grid');

            if (accidents.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无事故记录</p></div>';
                return;
            }

            grid.innerHTML = accidents.map(accident => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">事故记录 #${accident.id}</div>
                        <div class="card-meta">${formatDate(accident.shigushijian)}</div>
                    </div>
                    <div class="card-content">
                        <p><strong>严重程度：</strong><span class="safety-level safety-level-${getSeverityLevelClass(accident.yanzhongchengdu)}">${accident.yanzhongchengdu || '未知'}</span></p>
                        <p><strong>相关设施：</strong>${accident.sheshimingcheng || '未指定'}</p>
                        <p><strong>事故地点：</strong>${accident.shigudidian || '未指定'}</p>
                        <p class="card-description">${truncateText(accident.shigumiaoshu, 100)}</p>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewAccidentDetail(${accident.id})">查看详情</button>
                        ${currentUser ? `<button class="btn btn-primary btn-small" onclick="bookmarkItem('事故记录', ${accident.id}, '事故记录#${accident.id}')">收藏</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 提交反馈
        async function handleFeedbackSubmit(e) {
            e.preventDefault();

            if (!currentUser) {
                showMessage('请先登录', 'error');
                openModal('login-modal');
                return;
            }

            const feedbackData = {
                fankuileixing: document.getElementById('feedback-type').value,
                sheshiid: document.getElementById('feedback-facility').value || null,
                fankuibiaoti: document.getElementById('feedback-title').value,
                fankuineirong: document.getElementById('feedback-content').value,
                lianxifangshi: document.getElementById('feedback-contact').value,
                yonghuid: currentUser.id,
                zhuangtai: '待处理'
            };

            try {
                const response = await axios.post(`${API_BASE}/fankuiyijian/save`, feedbackData, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    showMessage('反馈提交成功！', 'success');
                    document.getElementById('feedback-form').reset();
                    searchMyFeedback();
                } else {
                    showMessage(response.data.msg || '提交失败', 'error');
                }
            } catch (error) {
                console.error('Submit feedback error:', error);
                showMessage('提交失败，请检查网络连接', 'error');
            }
        }

        // 搜索我的反馈
        async function searchMyFeedback() {
            if (!currentUser) return;

            const loading = document.getElementById('my-feedback-loading');
            const grid = document.getElementById('my-feedback-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('my-feedback-search').value;
                const statusFilter = document.getElementById('my-feedback-status-filter').value;

                let url = `${API_BASE}/fankuiyijian/page?page=1&limit=20&yonghuid=${currentUser.id}`;
                if (searchText) url += `&fankuibiaoti=${encodeURIComponent(searchText)}`;
                if (statusFilter) url += `&zhuangtai=${encodeURIComponent(statusFilter)}`;

                const response = await axios.get(url, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    const feedbacks = response.data.data.list || [];
                    renderMyFeedback(feedbacks);
                } else {
                    showMessage('加载反馈记录失败', 'error');
                }
            } catch (error) {
                console.error('Search my feedback error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染我的反馈
        function renderMyFeedback(feedbacks) {
            const grid = document.getElementById('my-feedback-grid');

            if (feedbacks.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无反馈记录</p></div>';
                return;
            }

            grid.innerHTML = feedbacks.map(feedback => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">${feedback.fankuibiaoti}</div>
                        <div class="card-meta">${formatDate(feedback.addtime)}</div>
                    </div>
                    <div class="card-content">
                        <p><strong>类型：</strong>${feedback.fankuileixing}</p>
                        <p><strong>状态：</strong><span class="status-tag ${getStatusClass(feedback.zhuangtai)}">${feedback.zhuangtai}</span></p>
                        <p class="card-description">${truncateText(feedback.fankuineirong, 100)}</p>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewFeedbackDetail(${feedback.id})">查看详情</button>
                    </div>
                </div>
            `).join('');
        }

        // 发起讨论
        async function handleDiscussionSubmit(e) {
            e.preventDefault();

            if (!currentUser) {
                showMessage('请先登录', 'error');
                openModal('login-modal');
                return;
            }

            const discussionData = {
                taolunzhuti: document.getElementById('discussion-title').value,
                sheshiid: document.getElementById('discussion-facility').value || null,
                taolunneirong: document.getElementById('discussion-content').value,
                faburenid: currentUser.id,
                faburen: currentUser.xingming || currentUser.zhanghao
            };

            try {
                const response = await axios.post(`${API_BASE}/discusssheshi/save`, discussionData, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    showMessage('讨论发起成功！', 'success');
                    document.getElementById('discussion-form').reset();
                    searchDiscussions();
                } else {
                    showMessage(response.data.msg || '发起失败', 'error');
                }
            } catch (error) {
                console.error('Submit discussion error:', error);
                showMessage('发起失败，请检查网络连接', 'error');
            }
        }

        // 搜索讨论
        async function searchDiscussions() {
            const loading = document.getElementById('discussions-loading');
            const grid = document.getElementById('discussions-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('discussion-search').value;
                const facilityFilter = document.getElementById('discussion-facility-filter').value;
                const sortBy = document.getElementById('discussion-sort').value;

                let url = `${API_BASE}/discusssheshi/page?page=1&limit=20`;
                if (searchText) url += `&taolunzhuti=${encodeURIComponent(searchText)}`;
                if (facilityFilter) url += `&sheshiid=${facilityFilter}`;

                const response = await axios.get(url);

                if (response.data.code === 0) {
                    const discussions = response.data.data.list || [];
                    renderDiscussions(discussions);
                } else {
                    showMessage('加载讨论失败', 'error');
                }
            } catch (error) {
                console.error('Search discussions error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染讨论列表
        function renderDiscussions(discussions) {
            const grid = document.getElementById('discussions-grid');

            if (discussions.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无讨论话题</p></div>';
                return;
            }

            grid.innerHTML = discussions.map(discussion => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">${discussion.taolunzhuti}</div>
                        <div class="card-meta">by ${discussion.faburen} · ${formatDate(discussion.addtime)}</div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">${truncateText(discussion.taolunneirong, 150)}</p>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewDiscussionDetail(${discussion.id})">查看详情</button>
                        ${currentUser ? `<button class="btn btn-primary btn-small" onclick="bookmarkItem('讨论话题', ${discussion.id}, '${discussion.taolunzhuti}')">收藏</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 搜索收藏
        async function searchBookmarks() {
            if (!currentUser) return;

            const loading = document.getElementById('bookmarks-loading');
            const grid = document.getElementById('bookmarks-grid');

            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const searchText = document.getElementById('bookmark-search').value;
                const typeFilter = document.getElementById('bookmark-type-filter').value;

                let url = `${API_BASE}/storeup/page?page=1&limit=20&userid=${currentUser.id}`;
                if (searchText) url += `&name=${encodeURIComponent(searchText)}`;
                if (typeFilter) url += `&type=${encodeURIComponent(typeFilter)}`;

                const response = await axios.get(url, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    const bookmarks = response.data.data.list || [];
                    renderBookmarks(bookmarks);
                } else {
                    showMessage('加载收藏失败', 'error');
                }
            } catch (error) {
                console.error('Search bookmarks error:', error);
                showMessage('搜索失败，请检查网络连接', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 渲染收藏列表
        function renderBookmarks(bookmarks) {
            const grid = document.getElementById('bookmarks-grid');

            if (bookmarks.length === 0) {
                grid.innerHTML = '<div class="card"><p>暂无收藏内容</p></div>';
                return;
            }

            grid.innerHTML = bookmarks.map(bookmark => `
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">${bookmark.name}</div>
                        <div class="card-meta">${formatDate(bookmark.addtime)}</div>
                    </div>
                    <div class="card-content">
                        <p><strong>类型：</strong>${bookmark.type}</p>
                        ${bookmark.picture ? `<img src="${bookmark.picture}" alt="图片" style="width: 100%; max-height: 150px; object-fit: cover; border-radius: 5px; margin-top: 10px;">` : ''}
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-outline btn-small" onclick="viewBookmarkDetail('${bookmark.type}', ${bookmark.refid})">查看原文</button>
                        <button class="btn btn-primary btn-small" onclick="removeBookmark(${bookmark.id})">取消收藏</button>
                    </div>
                </div>
            `).join('');
        }

        // 收藏功能
        async function bookmarkItem(type, refid, name) {
            if (!currentUser) {
                showMessage('请先登录', 'error');
                openModal('login-modal');
                return;
            }

            try {
                const response = await axios.post(`${API_BASE}/storeup/save`, {
                    userid: currentUser.id,
                    refid: refid,
                    tablename: getTableName(type),
                    name: name,
                    type: type
                }, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    showMessage('收藏成功！', 'success');
                } else {
                    showMessage(response.data.msg || '收藏失败', 'error');
                }
            } catch (error) {
                console.error('Bookmark error:', error);
                showMessage('收藏失败，请检查网络连接', 'error');
            }
        }

        // 取消收藏
        async function removeBookmark(id) {
            try {
                const response = await axios.post(`${API_BASE}/storeup/delete`, [id], {
                    headers: { 'token': localStorage.getItem('userToken') }
                });

                if (response.data.code === 0) {
                    showMessage('取消收藏成功！', 'success');
                    searchBookmarks();
                } else {
                    showMessage(response.data.msg || '操作失败', 'error');
                }
            } catch (error) {
                console.error('Remove bookmark error:', error);
                showMessage('操作失败，请检查网络连接', 'error');
            }
        }

        // 查看详情函数
        async function viewFacilityDetail(id) {
            try {
                const response = await axios.get(`${API_BASE}/sheshi/info/${id}`);
                if (response.data.code === 0) {
                    const facility = response.data.data;
                    showDetailModal('设施详情', renderFacilityDetail(facility));
                }
            } catch (error) {
                console.error('View facility detail error:', error);
                showMessage('加载详情失败', 'error');
            }
        }

        async function viewSafetyDetail(id) {
            try {
                const response = await axios.get(`${API_BASE}/anquanguifan/info/${id}`);
                if (response.data.code === 0) {
                    const safety = response.data.data;
                    showDetailModal('安全规范详情', renderSafetyDetail(safety));
                }
            } catch (error) {
                console.error('View safety detail error:', error);
                showMessage('加载详情失败', 'error');
            }
        }

        async function viewAccidentDetail(id) {
            try {
                const response = await axios.get(`${API_BASE}/shigujilu/info/${id}`);
                if (response.data.code === 0) {
                    const accident = response.data.data;
                    showDetailModal('事故记录详情', renderAccidentDetail(accident));
                }
            } catch (error) {
                console.error('View accident detail error:', error);
                showMessage('加载详情失败', 'error');
            }
        }

        async function viewFeedbackDetail(id) {
            try {
                const response = await axios.get(`${API_BASE}/fankuiyijian/info/${id}`, {
                    headers: { 'token': localStorage.getItem('userToken') }
                });
                if (response.data.code === 0) {
                    const feedback = response.data.data;
                    showDetailModal('反馈详情', renderFeedbackDetail(feedback));
                }
            } catch (error) {
                console.error('View feedback detail error:', error);
                showMessage('加载详情失败', 'error');
            }
        }

        async function viewDiscussionDetail(id) {
            try {
                const response = await axios.get(`${API_BASE}/discusssheshi/info/${id}`);
                if (response.data.code === 0) {
                    const discussion = response.data.data;
                    showDetailModal('讨论详情', renderDiscussionDetail(discussion));
                }
            } catch (error) {
                console.error('View discussion detail error:', error);
                showMessage('加载详情失败', 'error');
            }
        }

        function viewBookmarkDetail(type, refid) {
            switch(type) {
                case '设施信息':
                    viewFacilityDetail(refid);
                    break;
                case '安全规范':
                    viewSafetyDetail(refid);
                    break;
                case '事故记录':
                    viewAccidentDetail(refid);
                    break;
                case '讨论话题':
                    viewDiscussionDetail(refid);
                    break;
            }
        }

        // 详情渲染函数
        function renderFacilityDetail(facility) {
            return `
                <div class="detail-content">
                    ${facility.tupian ? `<img src="${facility.tupian}" alt="设施图片" style="width: 100%; max-height: 300px; object-fit: cover; border-radius: 5px; margin-bottom: 20px;">` : ''}
                    <div class="detail-info">
                        <p><strong>设施名称：</strong>${facility.sheshimingcheng}</p>
                        <p><strong>设施类型：</strong>${facility.sheshileixing || '未分类'}</p>
                        <p><strong>位置：</strong>${facility.weizhi || '未指定'}</p>
                        <p><strong>状态：</strong><span class="status-tag ${facility.zhuangtai === '正常运行' ? 'status-running' : 'status-maintenance'}">${facility.zhuangtai || '未知'}</span></p>
                        <p><strong>安全等级：</strong><span class="safety-level safety-level-${getSafetyLevelClass(facility.anquandengji)}">${facility.anquandengji || '未评估'}</span></p>
                        <p><strong>使用频率：</strong>${facility.shiyongpinlv || '未统计'}</p>
                        <p><strong>维护状态：</strong>${facility.weihuzhuangtai || '未知'}</p>
                        <p><strong>添加时间：</strong>${formatDate(facility.addtime)}</p>
                        ${facility.beizhu ? `<p><strong>备注：</strong>${facility.beizhu}</p>` : ''}
                    </div>
                </div>
            `;
        }

        function renderSafetyDetail(safety) {
            return `
                <div class="detail-content">
                    <div class="detail-info">
                        <p><strong>规范标题：</strong>${safety.guifanbiaoti}</p>
                        <p><strong>规范类型：</strong>${safety.guifanleixing || '未分类'}</p>
                        <p><strong>发布机构：</strong>${safety.fabujigou || '未知'}</p>
                        <p><strong>生效日期：</strong>${safety.shengxiaoriqi || '未指定'}</p>
                        <p><strong>添加时间：</strong>${formatDate(safety.addtime)}</p>
                        <div style="margin-top: 20px;">
                            <strong>规范内容：</strong>
                            <div style="margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; line-height: 1.6;">
                                ${safety.guifanneirong || '暂无内容'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderAccidentDetail(accident) {
            return `
                <div class="detail-content">
                    <div class="detail-info">
                        <p><strong>事故时间：</strong>${formatDate(accident.shigushijian)}</p>
                        <p><strong>严重程度：</strong><span class="safety-level safety-level-${getSeverityLevelClass(accident.yanzhongchengdu)}">${accident.yanzhongchengdu || '未知'}</span></p>
                        <p><strong>相关设施：</strong>${accident.sheshimingcheng || '未指定'}</p>
                        <p><strong>事故地点：</strong>${accident.shigudidian || '未指定'}</p>
                        <p><strong>记录时间：</strong>${formatDate(accident.addtime)}</p>
                        <div style="margin-top: 20px;">
                            <strong>事故描述：</strong>
                            <div style="margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; line-height: 1.6;">
                                ${accident.shigumiaoshu || '暂无描述'}
                            </div>
                        </div>
                        ${accident.chulicuoshi ? `
                            <div style="margin-top: 20px;">
                                <strong>处理措施：</strong>
                                <div style="margin-top: 10px; padding: 15px; background: #e8f5e8; border-radius: 5px; line-height: 1.6;">
                                    ${accident.chulicuoshi}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function renderFeedbackDetail(feedback) {
            return `
                <div class="detail-content">
                    <div class="detail-info">
                        <p><strong>反馈标题：</strong>${feedback.fankuibiaoti}</p>
                        <p><strong>反馈类型：</strong>${feedback.fankuileixing}</p>
                        <p><strong>状态：</strong><span class="status-tag ${getStatusClass(feedback.zhuangtai)}">${feedback.zhuangtai}</span></p>
                        <p><strong>提交时间：</strong>${formatDate(feedback.addtime)}</p>
                        ${feedback.lianxifangshi ? `<p><strong>联系方式：</strong>${feedback.lianxifangshi}</p>` : ''}
                        <div style="margin-top: 20px;">
                            <strong>反馈内容：</strong>
                            <div style="margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; line-height: 1.6;">
                                ${feedback.fankuineirong || '暂无内容'}
                            </div>
                        </div>
                        ${feedback.huifuneirong ? `
                            <div style="margin-top: 20px;">
                                <strong>回复内容：</strong>
                                <div style="margin-top: 10px; padding: 15px; background: #e8f5e8; border-radius: 5px; line-height: 1.6;">
                                    ${feedback.huifuneirong}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function renderDiscussionDetail(discussion) {
            return `
                <div class="detail-content">
                    <div class="detail-info">
                        <p><strong>讨论主题：</strong>${discussion.taolunzhuti}</p>
                        <p><strong>发布人：</strong>${discussion.faburen}</p>
                        <p><strong>发布时间：</strong>${formatDate(discussion.addtime)}</p>
                        <div style="margin-top: 20px;">
                            <strong>讨论内容：</strong>
                            <div style="margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; line-height: 1.6;">
                                ${discussion.taolunneirong || '暂无内容'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 工具函数
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        function showDetailModal(title, content) {
            document.getElementById('detail-modal-title').textContent = title;
            document.getElementById('detail-modal-content').innerHTML = content;
            openModal('detail-modal');
        }

        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.classList.add('show');

            setTimeout(() => {
                message.classList.remove('show');
            }, 3000);
        }

        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        function getSafetyLevelClass(level) {
            if (!level) return 'safe';
            const lowerLevel = level.toLowerCase();
            if (lowerLevel.includes('安全') || lowerLevel.includes('良好')) return 'safe';
            if (lowerLevel.includes('警告') || lowerLevel.includes('注意')) return 'warning';
            if (lowerLevel.includes('危险') || lowerLevel.includes('严重')) return 'danger';
            return 'safe';
        }

        function getSeverityLevelClass(level) {
            if (!level) return 'safe';
            const lowerLevel = level.toLowerCase();
            if (lowerLevel.includes('轻微')) return 'safe';
            if (lowerLevel.includes('一般') || lowerLevel.includes('中等')) return 'warning';
            if (lowerLevel.includes('严重') || lowerLevel.includes('重大')) return 'danger';
            return 'safe';
        }

        function getStatusClass(status) {
            if (!status) return '';
            switch(status) {
                case '正常运行':
                case '已解决':
                case '已完成':
                    return 'status-running';
                case '维护中':
                case '处理中':
                case '待处理':
                    return 'status-maintenance';
                default:
                    return '';
            }
        }

        function getTableName(type) {
            switch(type) {
                case '设施信息':
                    return 'sheshi';
                case '安全规范':
                    return 'anquanguifan';
                case '事故记录':
                    return 'shigujilu';
                case '讨论话题':
                    return 'discusssheshi';
                default:
                    return '';
            }
        }
    </script>
</body>
</html>