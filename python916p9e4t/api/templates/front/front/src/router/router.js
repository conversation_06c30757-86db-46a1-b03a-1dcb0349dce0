import VueRouter from 'vue-router'
//引入组件
import Index from '../pages'
import Home from '../pages/home/<USER>'
import Login from '../pages/login/login'
import Register from '../pages/register/register'
import Center from '../pages/center/center'
import Messages from '../pages/messages/list'
import Storeup from '../pages/storeup/list'
import News from '../pages/news/news-list'
import NewsDetail from '../pages/news/news-detail'
import payList from '../pages/pay'

import yunyingfangList from '../pages/yunyingfang/list'
import yunyingfangDetail from '../pages/yunyingfang/detail'
import yunyingfangAdd from '../pages/yunyingfang/add'
import jianguanrenyuanList from '../pages/jianguanrenyuan/list'
import jianguanrenyuanDetail from '../pages/jianguanrenyuan/detail'
import jianguanrenyuanAdd from '../pages/jianguanrenyuan/add'
import yonghuList from '../pages/yonghu/list'
import yonghuDetail from '../pages/yonghu/detail'
import yonghuAdd from '../pages/yonghu/add'
import facilitiesList from '../pages/facilities/list'
import facilitiesDetail from '../pages/facilities/detail'
import facilitiesAdd from '../pages/facilities/add'
import facilitiesforecastList from '../pages/facilitiesforecast/list'
import facilitiesforecastDetail from '../pages/facilitiesforecast/detail'
import facilitiesforecastAdd from '../pages/facilitiesforecast/add'
import shebeiweihujiluList from '../pages/shebeiweihujilu/list'
import shebeiweihujiluDetail from '../pages/shebeiweihujilu/detail'
import shebeiweihujiluAdd from '../pages/shebeiweihujilu/add'
import shigujiluList from '../pages/shigujilu/list'
import shigujiluDetail from '../pages/shigujilu/detail'
import shigujiluAdd from '../pages/shigujilu/add'
import renyuanpeixunList from '../pages/renyuanpeixun/list'
import renyuanpeixunDetail from '../pages/renyuanpeixun/detail'
import renyuanpeixunAdd from '../pages/renyuanpeixun/add'
import anquanguifanList from '../pages/anquanguifan/list'
import anquanguifanDetail from '../pages/anquanguifan/detail'
import anquanguifanAdd from '../pages/anquanguifan/add'
import newstypeList from '../pages/newstype/list'
import newstypeDetail from '../pages/newstype/detail'
import newstypeAdd from '../pages/newstype/add'
import aboutusList from '../pages/aboutus/list'
import aboutusDetail from '../pages/aboutus/detail'
import aboutusAdd from '../pages/aboutus/add'
import systemintroList from '../pages/systemintro/list'
import systemintroDetail from '../pages/systemintro/detail'
import systemintroAdd from '../pages/systemintro/add'
import discussfacilitiesList from '../pages/discussfacilities/list'
import discussfacilitiesDetail from '../pages/discussfacilities/detail'
import discussfacilitiesAdd from '../pages/discussfacilities/add'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err)
}

//配置路由
export default new VueRouter({
	routes:[
		{
      path: '/',
      redirect: '/index/home'
    },
		{
			path: '/index',
			component: Index,
			children:[
				{
					path: 'home',
					component: Home
				},
				{
					path: 'center',
					component: Center,
				},
				{
					path: 'pay',
					component: payList,
				},
				{
					path: 'messages',
					component: Messages
				},
				{
					path: 'storeup',
					component: Storeup
				},
				{
					path: 'news',
					component: News
				},
				{
					path: 'newsDetail',
					component: NewsDetail
				},
				{
					path: 'yunyingfang',
					component: yunyingfangList
				},
				{
					path: 'yunyingfangDetail',
					component: yunyingfangDetail
				},
				{
					path: 'yunyingfangAdd',
					component: yunyingfangAdd
				},
				{
					path: 'jianguanrenyuan',
					component: jianguanrenyuanList
				},
				{
					path: 'jianguanrenyuanDetail',
					component: jianguanrenyuanDetail
				},
				{
					path: 'jianguanrenyuanAdd',
					component: jianguanrenyuanAdd
				},
				{
					path: 'yonghu',
					component: yonghuList
				},
				{
					path: 'yonghuDetail',
					component: yonghuDetail
				},
				{
					path: 'yonghuAdd',
					component: yonghuAdd
				},
				{
					path: 'facilities',
					component: facilitiesList
				},
				{
					path: 'facilitiesDetail',
					component: facilitiesDetail
				},
				{
					path: 'facilitiesAdd',
					component: facilitiesAdd
				},
				{
					path: 'facilitiesforecast',
					component: facilitiesforecastList
				},
				{
					path: 'facilitiesforecastDetail',
					component: facilitiesforecastDetail
				},
				{
					path: 'facilitiesforecastAdd',
					component: facilitiesforecastAdd
				},
				{
					path: 'shebeiweihujilu',
					component: shebeiweihujiluList
				},
				{
					path: 'shebeiweihujiluDetail',
					component: shebeiweihujiluDetail
				},
				{
					path: 'shebeiweihujiluAdd',
					component: shebeiweihujiluAdd
				},
				{
					path: 'shigujilu',
					component: shigujiluList
				},
				{
					path: 'shigujiluDetail',
					component: shigujiluDetail
				},
				{
					path: 'shigujiluAdd',
					component: shigujiluAdd
				},
				{
					path: 'renyuanpeixun',
					component: renyuanpeixunList
				},
				{
					path: 'renyuanpeixunDetail',
					component: renyuanpeixunDetail
				},
				{
					path: 'renyuanpeixunAdd',
					component: renyuanpeixunAdd
				},
				{
					path: 'anquanguifan',
					component: anquanguifanList
				},
				{
					path: 'anquanguifanDetail',
					component: anquanguifanDetail
				},
				{
					path: 'anquanguifanAdd',
					component: anquanguifanAdd
				},
				{
					path: 'newstype',
					component: newstypeList
				},
				{
					path: 'newstypeDetail',
					component: newstypeDetail
				},
				{
					path: 'newstypeAdd',
					component: newstypeAdd
				},
				{
					path: 'aboutus',
					component: aboutusList
				},
				{
					path: 'aboutusDetail',
					component: aboutusDetail
				},
				{
					path: 'aboutusAdd',
					component: aboutusAdd
				},
				{
					path: 'systemintro',
					component: systemintroList
				},
				{
					path: 'systemintroDetail',
					component: systemintroDetail
				},
				{
					path: 'systemintroAdd',
					component: systemintroAdd
				},
				{
					path: 'discussfacilities',
					component: discussfacilitiesList
				},
				{
					path: 'discussfacilitiesDetail',
					component: discussfacilitiesDetail
				},
				{
					path: 'discussfacilitiesAdd',
					component: discussfacilitiesAdd
				},
			]
		},
		{
			path: '/login',
			component: Login
		},
		{
			path: '/register',
			component: Register
		},
	]
})
