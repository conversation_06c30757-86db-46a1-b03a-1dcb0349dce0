#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, send_from_directory
import os

# 创建前端蓝图
frontend_bp = Blueprint('frontend', __name__)

@frontend_bp.route('/')
def index():
    """首页路由 - 提供用户前端页面"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist'),
        'user-frontend.html'
    )

@frontend_bp.route('/user')
def user_frontend():
    """用户前端页面"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist'),
        'user-frontend.html'
    )

@frontend_bp.route('/admin')
def admin_frontend():
    """管理员前端页面 - 重定向到原有的Vue应用"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist'),
        'index.html'
    )

# 静态文件路由
@frontend_bp.route('/css/<path:filename>')
def css_files(filename):
    """CSS文件"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist/css'),
        filename
    )

@frontend_bp.route('/js/<path:filename>')
def js_files(filename):
    """JavaScript文件"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist/js'),
        filename
    )

@frontend_bp.route('/img/<path:filename>')
def img_files(filename):
    """图片文件"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist/img'),
        filename
    )

@frontend_bp.route('/fonts/<path:filename>')
def font_files(filename):
    """字体文件"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist/fonts'),
        filename
    )

@frontend_bp.route('/verifys/<path:filename>')
def verify_files(filename):
    """验证码相关文件"""
    return send_from_directory(
        os.path.join(os.path.dirname(__file__), 'templates/front/front/dist/verifys'),
        filename
    )
