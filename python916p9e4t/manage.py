# coding:utf-8
# author:ila
import threading
import webbrowser
import click,py_compile,os
from configparser import ConfigParser
from configs import configs
from utils.mysqlinit import Create_Mysql
from api import create_app
from api.exts import db
from api.models.user_model import *
from api.models.config_model import *
from api.models.brush_model import *
from flask import request
from gevent import pywsgi
from geventwebsocket.handler import WebSocketHandler
from urllib.parse import parse_qs

app = create_app(configs)
# 存储用户的 WebSocket 连接 {user_id: websocket}
clients = {}
@app.route('/python916p9e4t/ws')
def websocket_route():
    if not request.environ.get('wsgi.websocket'):  # 检查是否是 WebSocket 请求
        return "This is a WebSocket route."
    ws = request.environ['wsgi.websocket']  # 获取 WebSocket 连接
    # 解析查询参数
    query_string = request.environ.get('QUERY_STRING', '')
    params = parse_qs(query_string)
    # 提取 user_id 和 to_id
    user_id = params.get('user_id', [''])[0]
    to_id = params.get('to_id', [''])[0]
    if not user_id:
        return "user_id is required"
    clients[user_id] = ws
    print(f"Connected user_id: {user_id}, to_id: {to_id}")
    try:
        while not ws.closed:  # 循环监听客户端是否断开连接
            message = ws.receive()  # 接收来自客户端的消息
            if message:
                if not to_id:
                    for key,client in clients.items():
                        if key !=user_id:
                            client.send(f"user_id:{user_id}:{message}")  # 将消息发送给所有的客户端
                else:
                    recipient_ws = clients.get(to_id)
                    if recipient_ws and not recipient_ws.closed:  # 检查接收者的连接是否存在且未关闭
                        recipient_ws.send(f"user_id:{user_id}:{message}")  # 将消息发送给 to_id 对应的客户端
            else:
                break  # 如果接收到空消息，可能是客户端断开连接
    except Exception as e:
        print(f"Connection error: {e}")
    finally:
        # 客户端断开连接后，移除该用户的 WebSocket 连接
        if user_id in clients:
            del clients[user_id]
        print(f"User {user_id} disconnected.")

@click.group()
def sub():
    pass

@click.command()
@click.option("-v", default=0.1, type=float)
def verr(v):
    # VERSION = 0.1
    click.echo("py sub system version:{}".format(v))

def open_browser():
    webbrowser.open('http://localhost:8080/admin/dist/index.html')

@click.command()
def run():
    app.debug = configs['defaultConfig'].DEBUG
    threading.Timer(1, open_browser).start()
    server = pywsgi.WSGIServer((configs['defaultConfig'].HOST, configs['defaultConfig'].PORT), app, handler_class=WebSocketHandler)
    server.serve_forever()

@click.command()
def create_all():
    app = create_app(configs)
    with app.app_context():
        print("creat_all")
        db.create_all()

@click.command()
@click.option("--ini", type=str)
def initsql(ini):
    cp = ConfigParser()
    cp.read(ini,encoding="utf-8")
    sqltype = cp.get("sql", "type")
    database= cp.get("sql", "db")
    if sqltype == 'mysql':
        cm = Create_Mysql(ini)
        cm.create_db("CREATE DATABASE IF NOT EXISTS  `{}`  /*!40100 DEFAULT CHARACTER SET utf8 */ ;".format(database))
        with open("./db/mysql.sql", encoding="utf8") as f:
            createsql = f.read()
        createsql = "DROP TABLE" + createsql.split('DROP TABLE', 1)[-1]
        cm.create_tables(createsql.split(';\n')[:-1])
        cm.conn_close()
    elif sqltype == 'mssql':
        cm = Create_Mysql(ini)
        cm.create_db("CREATE DATABASE IF NOT EXISTS  `{}` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;".format(database))
        with open("./db/mssql.sql", encoding="utf8") as f:
            createsql = f.read()
        createsql = "DROP TABLE" + createsql.split('DROP TABLE', 1)[-1]
        cm.create_tables(createsql.split(';\n')[:-1])
        cm.conn_close()
    else:
        print('请修改当前面目录下的config.ini文件')

@click.command()
@click.option("--py_path", type=str)
def compile(py_path):
    print("py_path====>",py_path)
    py_compile.compile(py_path)


@click.command()
def replace_admin():
    filePath=os.path.join(os.getcwd(),"api/templates/front/index.html")
    if os.path.isfile(filePath):
        print(filePath)
        with open(filePath,"r",encoding="utf-8") as f:
            datas=f.read()
        datas=datas.replace('baseurl+"admin/dist/index.html#"','"http://localhost:8080/admin"')
        datas=datas.replace('baseurl+"admin/dist/index.html#/login"','"http://localhost:8080/admin"')

        with open(filePath,"w",encoding="utf-8") as f:
            f.write(datas)


sub.add_command(verr)
sub.add_command(run,"run")
sub.add_command(create_all,"create_all")
sub.add_command(initsql,"initsql")
sub.add_command(replace_admin,"replace_admin")

if __name__ == "__main__":
    sub()
